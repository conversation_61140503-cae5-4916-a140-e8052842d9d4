// 环保监控一张图 相关接口

import instance from "./request";
import base from "./base";

// 类型定义
export interface ApiResponse<T = any> {
  code: number;
  msg: string;
  data: T;
}

// 预警统计数据类型
export interface AlarmStatisticsData {
  startTime?: string;
  endTime?: string;
  deptId?: string;
  [key: string]: any;
}

// 设备地图数据类型
export interface DeviceMapData {
  deptId?: string;
  monitorType?: string;
  [key: string]: any;
}

// 点位信息类型
export interface PointInfo {
  id: string;
  name: string;
  longitude: number;
  latitude: number;
  [key: string]: any;
}

// 监测数据类型
export interface MonitorData {
  id: string;
  monitorType: string;
  [key: string]: any;
}

// 摄像头流参数类型
export interface CameraStreamParams {
  cameraIndexCode: string;
  streamType: number;
  protocol: string;
  transmode: number;
}

// 监测统计参数类型
export interface MonitorStatisticsParams {
  startTime?: string;
  endTime?: string;
  monitorType?: string;
  [key: string]: any;
}

// 监测列表参数类型
export interface MonitorListParams {
  monitorType: string;
  pageNum?: number;
  pageSize?: number;
  [key: string]: any;
}

// 报警分页参数类型
export interface AlarmPageParams {
  current: number;
  size: number;
  pointId?: string;
  startTime?: string;
  endTime?: string;
  [key: string]: any;
}

// 甲烷模块参数类型
export interface MethaneParams {
  deptId?: string;
  pointId?: string;
  startTime?: string;
  endTime?: string;
  type?: string;
  [key: string]: any;
}

// 预警分布数据：
export function initAlarmStatistics(data: AlarmStatisticsData): Promise<ApiResponse> {
    return instance({
        url: `${base.url}/picture/initAlarmStatistics`,
        method: "post",
        data
    });
}

// 预警分布数据-报警时长：
export function initAlarmTimeStatistics(data: AlarmStatisticsData): Promise<ApiResponse> {
    return instance({
        url: `${base.url}/picture/initAlarmTimeStatistics`,
        method: "post",
        data
    });
}

// 预警分布数据-报警时长-弹出框列表：
export function initAlarmList(data: AlarmStatisticsData): Promise<ApiResponse> {
    return instance({
        url: `${base.url}/picture/initAlarmList`,
        method: "post",
        data
    });
}

// 报警趋势数据：
export function initAlarmTrendStatistics(data: AlarmStatisticsData): Promise<ApiResponse> {
    return instance({
        url: `${base.url}/picture/initAlarmTrendStatistics`,
        method: "post",
        data
    });
}

// 设备地图：
export function initDeviceMap(data: DeviceMapData): Promise<ApiResponse<PointInfo[]>> {
    return instance({
        url: `${base.url}/picture/getPointList`,
        method: "post",
        data
    });
}

// 根据id获取设备信息
export function getPointInfo(data: { id: string }): Promise<ApiResponse<PointInfo>> {
    return instance({
        url: `${base.url}/picture/getPointInfo`,
        method: "post",
        data
    });
}

// 污染源在线监测设备分布
export function initDeviceStatistics(data: DeviceMapData): Promise<ApiResponse> {
    return instance({
        url: `${base.url}/picture/initDeviceStatistics`,
        method: "post",
        data
    });
}

// 污染源在线管理模块废水
export function initMonitWastewaterList(data: MonitorListParams): Promise<ApiResponse> {
    return instance({
        url: `${base.url}/picture/initMonitWastewaterList`,
        method: "post",
        data
    });
}

// 污染源在线管理废气
export function initMonitWastegasList(data: MonitorListParams): Promise<ApiResponse> {
    return instance({
        url: `${base.url}/picture/initMonitWasteGasList`,
        method: "post",
        data
    });
}

// 报警情况
export function initEnvinfoWarnList(data: { monitorType: string }): Promise<ApiResponse> {
    return instance({
        url: `${base.url}/picture/initEnvinfoWarnList`,
        method: "post",
        data
    });
}

// 获取甲烷模块组织机构
export function getJoinDept(params?: MethaneParams): Promise<ApiResponse> {
    return instance({
        url: `${base.url3}/external-laserCloud/getJoinDept`,
        method: "get",
        params
    });
}

// 获取甲烷模块组织机构下点位
export function getDeptPoint(params: MethaneParams): Promise<ApiResponse> {
    return instance({
        url: `${base.url3}/external-laserCloud/getDeptPoint`,
        method: "get",
        params
    });
}

// 获取甲烷模块组织机构下点位下实时数据
export function getRealTimeData(params: MethaneParams): Promise<ApiResponse> {
    return instance({
        url: `${base.url3}/external-laserCloud/getRealTimeData`,
        method: "get",
        params
    });
}

// 获取甲烷模块组织机构下点位下历史数据
export function getHistoryData(params: MethaneParams): Promise<ApiResponse> {
    return instance({
        url: `${base.url3}/external-laserCloud/getHistoryData`,
        method: "get",
        params
    });
}

// -------- htx --------
// 树节点
export function getTreeNode(params?: { deptId?: string }): Promise<ApiResponse> {
    return instance({
        url: `${base.url}/W58/getTreeNode`,
        method: "get",
        params
    });
}

// 摄像头列表
export function getCameraList(params: { siteId: string }): Promise<ApiResponse> {
    return instance({
        url: `${base.url}/W14/pullCameraList`,
        method: "get",
        params
    });
}

// 摄像头视频流
export function getCameraStream(params: CameraStreamParams): Promise<ApiResponse> {
    return instance({
        url: `${base.url}/W14/getCameraStream`,
        method: "get",
        params
    });
}

// 检测点信息
export function getMonitorInfo(params: { id: string; monitorType: string }): Promise<ApiResponse<MonitorData>> {
    return instance({
        url: `${base.url}/W14/getMonitorInfo`,
        method: "get",
        params
    });
}

// 实时监测结论
export function getLatestRecord(params: { mBasicMonitorTbId: string }): Promise<ApiResponse> {
    return instance({
        url: `${base.url}/W41/getLatestRecord`,
        method: "get",
        params
    });
}

// 实时监测结论
export function getMonitorInfoById(params: { id: string }): Promise<ApiResponse<MonitorData>> {
    return instance({
        url: `${base.url}/W14/getMonitorInfoById`,
        method: "get",
        params
    });
}

// 监测点信息列表
export function getMonitorInfoList(params: { monitorType?: string }): Promise<ApiResponse> {
    return instance({
        url: `${base.url}/W14/getMonitorInfoList`,
        method: "get",
        params
    });
}

// 根据上级id获取排放口列表
export function getSiteList(id: string): Promise<ApiResponse> {
    return instance({
        url: `${base.url}/W14/getSiteList/` + id,
        method: "get",
    });
}

// 获取详情
export function getSiteInfo(id: string): Promise<ApiResponse<MonitorData>> {
    return instance({
        url: `${base.url}/W14/getSiteInfo/` + id,
        method: "get",
    });
}

// 获取近3小时数据
export function getMonitor3Hour(params: {
    monitorId: string;
    monitorType: string;
    hour?: number
}): Promise<ApiResponse> {
    return instance({
        url: `${base.url}/W14/getMonitor3Hour`,
        method: "get",
        params
    });
}

// 获取气体泄漏报警统计数据
export function getAlarmPage(data: AlarmPageParams): Promise<ApiResponse> {
    return instance({
        url: `${base.url}/alarm/page`,
        method: "post",
        data
    });
}

/**
 * 获取-监测点信息统计
 * @param params
 * @returns {*}
 */
export function getMonitorStatistics(params: MonitorStatisticsParams): Promise<ApiResponse> {
    return instance({
        url: `${base.url}/W14/getMonitorStatistics`,
        method: "get",
        params
    });
}

/**
 * 获取-废水/废气实时监测
 * @param params
 * @returns {*}
 */
export function getMonitorList(params: MonitorListParams): Promise<ApiResponse> {
    return instance({
        url: `${base.url}/W14/getMonitorList`,
        method: "get",
        params
    });
}



