<template>
  <div>
    <a-modal
      v-model:open="open"
      title="点位信息"
      :width="1000"
      :destroy-on-close="true"
      :footer="null"
    >
      <a-tabs v-model:activeKey="activeName" @change="handleClick">
        <a-tab-pane key="0" tab="点位统计" />
        <a-tab-pane key="1" tab="点位详情" />
      </a-tabs>
      <div style="margin-top: 20px" v-if="activeName === '0'">
        <div class="details-dialog-content-top" v-for="item in chartData" :key="item.id">
          <a-card>
            <template #title>
              <a-button type="link" @click="pointHandle(item.id)">{{ item.name }}</a-button>
            </template>
            <!--获取废水信息-->
            <a-row v-if="item.monitorType === '10'">
              <a-col :span="12">
                <line-chart 
                  :chart-data="item.childChart.cWater011" 
                  :time-data="item.childChart.mTime"
                  :title="`COD 报警阈值[${chartData.pointValue1 == undefined ? '-' : chartData.pointValue1}]`"
                  :point-value="chartData.pointValue1" 
                  @child-chart="childHandle" 
                  :type="'COD'"
                  :site-id="item.id" 
                  :monitor-type="item.monitorType"
                />
              </a-col>
              <a-col :span="12">
                <line-chart 
                  :chart-data="item.childChart.cWater060" 
                  :time-data="item.childChart.mTime"
                  :title="`氨氮 报警阈值[${chartData.pointValue2 == undefined ? '-' : chartData.pointValue2}]`"
                  :point-value="chartData.pointValue2" 
                  @child-chart="childHandle" 
                  :type="'氨氮'"
                  :site-id="item.id" 
                  :monitor-type="item.monitorType"
                />
              </a-col>
            </a-row>
            <!--获取废气信息-->
            <a-row v-if="item.monitorType === '20'">
              <a-col :span="12">
                <line-chart 
                  :chart-data="item.childChart.cAir02R" 
                  :time-data="item.childChart.mTime"
                  :title="`SO2 报警阈值[${chartData.pointValue1 == undefined ? '-' : chartData.pointValue1}]`"
                  :point-value="chartData.pointValue1"
                  @child-chart="childHandle" 
                  :type="'SO2'"
                  :site-id="item.id" 
                  :monitor-type="item.monitorType"
                />
              </a-col>
              <a-col :span="12">
                <line-chart 
                  :chart-data="item.childChart.cAir03R" 
                  :time-data="item.childChart.mTime"
                  :title="`NOX 报警阈值[${chartData.pointValue2 == undefined ? '-' : chartData.pointValue2}]`"
                  :point-value="chartData.pointValue2" 
                  @child-chart="childHandle" 
                  :type="'NOX'"
                  :site-id="item.id" 
                  :monitor-type="item.monitorType"
                />
              </a-col>
            </a-row>
          </a-card>
        </div>
      </div>
      <div style="margin-top: 20px" v-if="activeName === '1'">
        <a-button 
          type="link" 
          @click="toMonitor" 
          class="send" 
          :icon="h(SendOutlined)"
        />
        <div class="details-dialog-content-title">
          <a-row>
            <a-col :span="8">
              <div style="line-height: 20px; margin-top: 10px">单位：{{ orgNode.sitePathName }}</div>
            </a-col>
            <a-col :span="8">
              <div style="line-height: 20px; margin-top: 10px">监测点：{{ orgNode.siteShortName }}</div>
            </a-col>
            <a-col :span="8">
              <a-select v-model:value="monitorId" @change="childDataChange" placeholder=" ">
                <a-select-option 
                  v-for="item in siteList" 
                  :key="item.id" 
                  :value="item.id"
                >
                  {{ item.monitorName }}
                </a-select-option>
              </a-select>
            </a-col>
          </a-row>
        </div>
        <div class="details-dialog-content-top">
          <div class="details-dialog-content-top-content">
            <a-card>
              <template #title>
                <span>监测项目信息</span>
                <span class="font-size-12px">{{ mTimeMoniteor }}</span>
              </template>
              <div>
                <a-table
                  :data-source="tableData"
                  :pagination="false"
                  :scroll="{ y: 200 }"
                  :row-class-name="getRowClassName"
                  @row-click="rowClick"
                >
                  <a-table-column title="序号" width="80" align="center">
                    <template #default="{ index }">
                      {{ index + 1 }}
                    </template>
                  </a-table-column>
                  <a-table-column 
                    title="监测项目" 
                    data-index="name" 
                    align="center" 
                  />
                  <a-table-column 
                    title="监测值" 
                    data-index="val" 
                    align="center" 
                  />
                </a-table>
              </div>
            </a-card>
          </div>
          <div class="details-dialog-content-top-content">
            <a-card>
              <template #title>
                <span>实时监测趋势</span>
              </template>
              <line-chart 
                :chart-data="chartData1" 
                :time-data="mTime1" 
                :title="title" 
                :height="200"
                :type="title"
              />
            </a-card>
          </div>
        </div>
        <div class="details-dialog-content-top w-full">
          <a-card>
            <template #title>
              <span>监控设备信息</span>
            </template>
            <div
              v-for="(item, index) in deviceList"
              :key="item.indexCode"
              class="text item"
              @click="overview(item, index)"
            >
              {{ item.name + "#" + (index + 1) }}
            </div>
          </a-card>
        </div>
        <div class="details-dialog-content-top w-full">
          <a-card>
            <template #title>
              <span>排放口基础信息</span>
            </template>
            <a-button type="link" @click="jumpHandle">点击查看详情</a-button>
          </a-card>
        </div>
      </div>
    </a-modal>
    
    <a-modal
      v-model:open="videoDialog"
      :width="1000"
      title=""
      :footer="null"
    >
      <div style="margin-bottom: 10px; color: #fff">{{ videoTitle }}</div>
      <div id="video_player" class="video-box"></div>
      <template #footer>
        <a-button @click="closeVideo">返回</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted, onBeforeUnmount, nextTick, h } from 'vue';
import { useRouter } from 'vue-router';
import { SendOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import {
  getCameraStream,
  getLatestRecord,
  getMonitor3Hour,
  getMonitorInfo,
  getSiteInfo,
  getSiteList
} from '@/http/environmental-overview';
import { excetptionTypeCode, gasCode, latestRecordCode, monitorTypeCode, waterCode } from '@/http/constant';
import { formatType } from '@/utils/formatVal';
import LineChart from './LineChart.vue';

interface DeviceItem {
  id: string;
  name: string;
  indexCode: string;
  [key: string]: any;
}

interface Props {
  deviceList: DeviceItem[];
  siteShortName: string;
}

interface OrgNode {
  id: string;
  monitorType: string;
  sitePathName: string;
  siteShortName: string;
}

interface TableData {
  name: string;
  val: any;
  isSelected: boolean;
}

interface ChartData {
  id: string;
  name: string;
  monitorType: string;
  pointValue1: number;
  pointValue2: number;
  childChart: {
    mTime: string[];
    cWater011: number[];
    cWater060: number[];
    cAir02R: number[];
    cAir03R: number[];
  };
}

const props = withDefaults(defineProps<Props>(), {
  deviceList: () => [],
  siteShortName: '',
});

const router = useRouter();

const monitorId = ref('');
const activeName = ref('0');
const latestRecord = ref<any[]>([]);
const mTimeMoniteor = ref('');
const mTimeLatest = ref('');
const orgNode = ref<Partial<OrgNode>>({});
const videoTitle = ref('');
const url = ref('');
const player = ref<any>(null);
const videoDialog = ref(false);
const open = ref(false);
const tableData = ref<TableData[]>([]);
const selectData = ref<TableData[]>([]);
const timer = ref<NodeJS.Timeout | null>(null);
const timerMonitor = ref<NodeJS.Timeout | null>(null);

const colorArr = [
  { line: 'rgb(30, 131, 213)', marker: 'rgba(30, 131, 213, 0.5)' },
  { line: 'rgb(83, 240, 233)', marker: 'rgba(83, 240, 233, 0.5)' },
  { line: 'rgb(46, 199, 201)', marker: 'rgba(46, 199, 201, 0.5)' },
  { line: 'rgb(182, 162, 222)', marker: 'rgba(182, 162, 222, 0.5)' },
  { line: 'rgb(90, 177, 239)', marker: 'rgba(90, 177, 239, 0.5)' },
  { line: 'rgb(255, 185, 128)', marker: 'rgba(255, 185, 128, 0.5)' },
  { line: 'rgb(216, 122, 128)', marker: 'rgba(216, 122, 128, 0.5)' },
  { line: 'rgb(141, 152, 179)', marker: 'rgba(141, 152, 179, 0.5)' },
  { line: 'rgb(229, 207, 13)', marker: 'rgba(229, 207, 13, 0.5)' },
  { line: 'rgb(151, 181, 82)', marker: 'rgba(151, 181, 82, 0.5)' },
  { line: 'rgb(149, 112, 109)', marker: 'rgba(149, 112, 109, 0.5)' },
  { line: 'rgb(220, 105, 170)', marker: 'rgba(220, 105, 170, 0.5)' },
  { line: 'rgb(7, 162, 164)', marker: 'rgba(7, 162, 164, 0.5)' },
  { line: 'rgb(154, 127, 209)', marker: 'rgba(154, 127, 209, 0.5)' },
  { line: 'rgb(88, 141, 213)', marker: 'rgba(88, 141, 213, 0.5)' },
  { line: 'rgb(245, 153, 78)', marker: 'rgba(245, 153, 78, 0.5)' },
  { line: 'rgb(192, 80, 80)', marker: 'rgba(192, 80, 80, 0.5)' },
  { line: 'rgb(89, 103, 140)', marker: 'rgba(89, 103, 140, 0.5)' },
  { line: 'rgb(201, 171, 0)', marker: 'rgba(201, 171, 0, 0.5)' },
  { line: 'rgb(126, 176, 10)', marker: 'rgba(126, 176, 10, 0.5)' },
  { line: 'rgb(111, 85, 83)', marker: 'rgba(111, 85, 83, 0.5)' },
  { line: 'rgb(193, 64, 137)', marker: 'rgba(193, 64, 137, 0.5)' },
];

const siteList = ref<any[]>([]);
const chartData = ref<ChartData[]>([]);
const lastExecutionTime = ref<string | null>(null);
const timer1 = ref<NodeJS.Timeout | null>(null);
const chartData1 = ref<number[]>([]);
const mTime1 = ref<string[]>([]);
const title = ref('');

// 方法
const getSiteListData = (id: string, sitePathName: string) => {
  getSiteList(id).then((res: any) => {
    if (res.data.length > 0 && res.code === 200) {
      siteList.value = res.data;
      monitorId.value = res.data[0].id;
      orgNode.value = {
        id: res.data[0].id,
        monitorType: res.data[0].monitorType,
        sitePathName: sitePathName,
        siteShortName: res.data[0].monitorName,
      };
      getMonitorInfos(orgNode.value.id!, orgNode.value.monitorType!);
      getMonitor3HourData();
    } else {
      siteList.value = [];
      monitorId.value = '';
      orgNode.value = {};
    }
  });
};

// tabs切换
const handleClick = (key: string) => {
  if (key === '1') {
    pointHandle(siteList.value[0].id);
  }
};

// 获取视频流
const getCameraStreams = async (indexCode: string) => {
  const params = {
    cameraIndexCode: indexCode,
    streamType: 0,
    protocol: 'ws',
    transmode: 1,
  };
  const res = await getCameraStream(params);
  url.value = res.data.data.url;
};

// 预览视频
const overview = async (item: any, index: number) => {
  videoTitle.value = props.siteShortName + '#' + (index + 1);
  await getCameraStreams(item.indexCode);
  const videoUrl = url.value;
  videoDialog.value = true;
  open.value = false;
  await nextTick();
  
  // TODO h5播放地址  
  player.value = new (window as any).JSPlugin({
    szId: 'video_player',
    szBasePath: 'http://localhost:1234/h5player',
    iWidth: 600,
    iHeight: 400,
  });
  
  player.value
    .JS_Play(
      videoUrl,
      {
        playURL: videoUrl,
      },
      0
    )
    .then(
      (res: any) => {
        console.log('success', res);
      },
      (err: any) => {
        console.log('err111', err);
      }
    );
};

// 关闭视频
const closeVideo = () => {
  player.value.JS_StopRealPlayAll().then(
    () => {
      console.log('JS_stop');
    },
    (err: any) => {
      console.log('JS_stop failed:', err);
    }
  );
  videoDialog.value = false;
  open.value = true;
};

// 检测点信息
const getMonitorInfos = async (id: string, monitorType: string) => {
  const res = await getMonitorInfo({ id, monitorType });
  console.log('res', res);
  if (res.code !== 200) {
    message.error(res.msg);
    tableData.value = [];
  } else {
    open.value = true;
    mTimeMoniteor.value = res.data.mTime;
    const dataCode = monitorType == '10' ? waterCode : gasCode;
    const list: TableData[] = [];
    for (let key in res.data) {
      dataCode.forEach((item) => {
        if (item.value == key) {
          list.push({
            name: item.label,
            val: res.data[key],
            isSelected: false,
          });
        }
      });
    }
    tableData.value = list;
    if (list.length > 0) {
      rowClick(list[0]);
    }
  }
};

// 实时监测结论
const getLatestRecords = async (mBasicMonitorTbId: string) => {
  const res = await getLatestRecord({ mBasicMonitorTbId });
  console.log('LatestRecord', res);
  if (res.code !== 200) {
    latestRecord.value = [];
  } else {
    const data = res.data;
    data['exceptionType'] = formatType(data.exceptionType, excetptionTypeCode);
    data['monitorType'] = formatType(data.monitorType, monitorTypeCode);
    mTimeLatest.value = data.mTime;
    const list: any[] = [];
    latestRecordCode.forEach((item) => {
      for (let key in data) {
        if (item.value == key) {
          list.push({
            name: item.label,
            val: res.data[key],
          });
        }
      }
    });
    latestRecord.value = list;
    console.log('latestRecords', latestRecord.value);
  }
};

// 跳转到监测数据查询
const toMonitor = () => {
  router.push({
    path: '/riskOpera/monitoringDataQuery',
    query: orgNode.value,
  });
  clearInterval(timer.value!);
  timer.value = null;
  clearInterval(timerMonitor.value!);
  timerMonitor.value = null;
};

const rowClick = (row: TableData) => {
  console.log(row);
  chartData1.value = [];
  mTime1.value = [];
  title.value = row.name;
  const dataCode = orgNode.value.monitorType == '10' ? waterCode : gasCode;
  let value = '';
  dataCode.forEach((item) => {
    if (item.label == row.name) {
      value = item.value;
    }
  });

  getMonitor3Hour({
    monitorId: orgNode.value.id!,
    monitorType: orgNode.value.monitorType!,
    hour: 24
  }).then((res: any) => {
    res.data.forEach((item: any) => {
      mTime1.value.push(item.mTime1);
      chartData1.value.push(item[value]);
    });
  });
};

const getRowClassName = ({ row }: { row: TableData }) => {
  // 根据 isSelected 状态返回类名
  if (row.isSelected) {
    return 'current-row';
  }
  return '1';
};

const openDialog = (val: any) => {
  chartData.value = [];
  console.log('弹窗');
  open.value = true;
  getSiteListData(val.id, val.sitePathName);

  // 清除定时器，避免内存泄漏
  if (timer.value) {
    clearInterval(timer.value);
  }
  // 启动定时器，每隔 10 分钟执行一次
  timer.value = setInterval(() => {
    executeTask();
  }, 10 * 60 * 1000); // 10 分钟 = 600,000 毫秒

  // 首次加载时立即执行一次
  executeTask();
};

// 跳转
const jumpHandle = () => {
  router.push({
    path: '/riskOpera/basicInformationManagement',
    query: orgNode.value,
  });
};

// 点击选择
const pointHandle = (siteId: string) => {
  activeName.value = '1';
  chartData1.value = [];
  mTime1.value = [];
  title.value = '';
  getSiteInfo(siteId).then((res: any) => {
    if (res.code === 200) {
      monitorId.value = res.data.id;
      orgNode.value = {
        id: res.data.id,
        monitorType: res.data.monitorType,
        sitePathName: orgNode.value.sitePathName,
        siteShortName: res.data.monitorName,
      };
      getMonitorInfos(orgNode.value.id!, orgNode.value.monitorType!);
    } else {
      monitorId.value = '';
      orgNode.value = {};
    }
  });
};

//下拉选择框
const childDataChange = () => {
  getSiteInfo(monitorId.value).then((res: any) => {
    if (res.code === 200) {
      monitorId.value = res.data.id;
      orgNode.value = {
        id: res.data.id,
        monitorType: res.data.monitorType,
        sitePathName: orgNode.value.sitePathName,
        siteShortName: res.data.monitorName,
      };
      getMonitorInfos(orgNode.value.id!, orgNode.value.monitorType!);
    } else {
      monitorId.value = '';
      orgNode.value = {};
    }
  });
};

//获取图表数据
const getMonitor3HourData = () => {
  chartData.value = [];
  console.log('this.siteList----', siteList.value);
  for (let i = 0; i < siteList.value.length; i++) {
    getMonitor3Hour({
      'monitorId': siteList.value[i].id,
      'monitorType': siteList.value[i].monitorType
    }).then(res => {
      let mTime: string[] = [];
      let cWater011: number[] = [];
      let cWater060: number[] = [];
      let cAir02R: number[] = [];
      let cAir03R: number[] = [];
      res.data.forEach((item: any) => {
        mTime.push(item.mTime1);
        cWater011.push(item.cwater011);
        cWater060.push(item.cwater060);
        cAir02R.push(item.cair02R);
        cAir03R.push(item.cair03R);
      });
      chartData.value.push({
        id: siteList.value[i].id,
        name: siteList.value[i].monitorName,
        monitorType: siteList.value[i].monitorType,
        pointValue1: 0,
        pointValue2: 0,
        childChart: {
          mTime: mTime,
          cWater011: cWater011,
          cWater060: cWater060,
          cAir02R: cAir02R,
          cAir03R: cAir03R
        }
      });
      if (res.data[i] != null) {
        console.log(res.data[i]);
        if (siteList.value[i].monitorType == '10') {
          if (chartData.value.cWater011 !== null && chartData.value.cWater011 !== []) {
            chartData.value.pointValue1 = Number(res.data[i].cwater011Upper);
            console.log('阈值011', res.data[i].cwater011Upper);
          }
          if (chartData.value.cWater060 !== null && chartData.value.cWater060 !== []) {
            chartData.value.pointValue2 = Number(res.data[i].cwater060Upper);
            console.log('阈值060', res.data[i].cwater060Upper);
          }
        }
        if (siteList.value[i].monitorType == '20') {
          if (chartData.value.cAir02R !== null && chartData.value.cAir02R !== []) {
            chartData.value.pointValue1 = Number(res.data[i].so2Range.split('/')[0] == undefined ? 0 : res.data[i].so2Range.split('/')[0]);
            console.log('阈值SO2', res.data[i].so2Range.split('/')[0]);
          }
          if (chartData.value.cAir03R !== null && chartData.value.cAir03R !== []) {
            chartData.value.pointValue2 = Number(res.data[i].noxRange.split('/')[0] == undefined ? 0 : res.data[i].noxRange.split('/')[0]);
            console.log('阈值NOX', res.data[i].noxRange.split('/')[0]);
          }
        }
      }
      console.log(chartData.value);
    });
  }
  console.log('获取图表数据', chartData.value);
};

// 需要定时执行的方法
const executeTask = () => {
  lastExecutionTime.value = new Date().toLocaleString(); // 更新上次执行时间
  console.log('任务执行时间:', lastExecutionTime.value);
  // 在这里编写需要执行的逻辑
  // getMonitor3HourData()
};

const childHandle = (e: any) => {
  console.log('childHandle---', e);
  getMonitor3Hour({
    'monitorId': e.siteId,
    'monitorType': e.monitorType,
    'hour': 24
  }).then(res => {
    let mTime: string[] = [];
    let cWater011: number[] = [];
    let cWater060: number[] = [];
    let cAir02R: number[] = [];
    let cAir03R: number[] = [];
    for (let i = 0; i < chartData.value.length; i++) {
      if (e.siteId == chartData.value[i].id) {
        console.log('筛选数据', chartData.value[i]);
        if (e.type === 'COD') {
          res.data.forEach((item: any) => {
            mTime.push(item.mTime1);
            cWater011.push(item.cwater011);
          });
          chartData.value.pointValue1 = Number(res.data[i].cwater011Upper);
          chartData.value[i].childChart.mTime = mTime;
          chartData.value[i].childChart.cWater011 = cWater011;
        }
        if (e.type === '氨氮') {
          res.data.forEach((item: any) => {
            mTime.push(item.mTime1);
            cWater060.push(item.cwater060);
          });
          chartData.value.pointValue2 = Number(res.data[i].cwater060Upper);
          chartData.value[i].childChart.mTime = mTime;
          chartData.value[i].childChart.cWater060 = cWater060;
        }
        if (e.type === 'SO2') {
          res.data.forEach((item: any) => {
            mTime.push(item.mTime1);
            cAir02R.push(item.cair02R);
          });
          chartData.value.pointValue1 = Number(res.data[i].so2Range.split('/')[0] == undefined ? 0 : res.data[i].so2Range.split('/')[0]);
          chartData.value[i].childChart.mTime = mTime;
          chartData.value[i].childChart.cAir02R = cAir02R;
        }
        if (e.type === 'NOX') {
          res.data.forEach((item: any) => {
            mTime.push(item.mTime1);
            cAir03R.push(item.cair03R);
          });
          chartData.value.pointValue2 = Number(res.data[i].noxRange.split('/')[0] == undefined ? 0 : res.data[i].noxRange.split('/')[0]);
          chartData.value[i].childChart.mTime = mTime;
          chartData.value[i].childChart.cAir03R = cAir03R;
        }
      }
    }
  });
  console.log(chartData.value);
};

// 监听
watch(() => open.value, (val) => {
  if (!val) {
    clearInterval(timer.value!);
    timer.value = null;
    clearInterval(timerMonitor.value!);
    timerMonitor.value = null;
  }
}, { deep: true, immediate: true });

watch(() => selectData.value, (val) => {
  clearInterval(timerMonitor.value!);
  timerMonitor.value = null;
  if (selectData.value.length > 0 && open.value) {
    timerMonitor.value = setInterval(() => {
      getMonitorInfos(orgNode.value.id!, orgNode.value.monitorType!);
    }, 1000);
  } else {
    clearInterval(timerMonitor.value!);
    timerMonitor.value = null;
  }
});

onBeforeUnmount(() => {
  clearInterval(timer.value!);
  timer.value = null;
  clearInterval(timerMonitor.value!);
  timerMonitor.value = null;
});

// 暴露方法
defineExpose({
  openDialog
});
</script>

<style lang="less" scoped>
.mr-100px {
  margin-right: 100px;
}

.w-full {
  width: 100%;
}

.send {
  position: absolute;
  top: 14px;
  right: 50px;
  font-size: 15px;
  color: #03abed;
  cursor: pointer;
}

.details-dialog-content-title {
  font-size: 12px;
  color: #ffffff;
}

.details-dialog-content-top {
  margin-top: 16px;
  width: 100%;
  display: flex;
  justify-content: space-between;

  .details-dialog-content-top-content {
    width: calc(50% - 4px);
  }
}

.details-dialog-content-top-result {
  margin-top: 8px;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;

  span {
    margin-top: 8px;
    display: inline-block;
    width: 50%;
  }
}

:deep(.ant-card) {
  background: transparent;
  border: 1px solid rgb(43 141 197);
  color: #ffffff;
  width: 100%;

  .ant-card-head {
    padding: 8px;
    border-color: rgb(43 141 197);

    .ant-card-head-title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      span:nth-child(1) {
        font-size: 14px;
      }

      span:nth-child(2) {
        font-size: 12px;
      }
    }
  }

  .ant-card-body {
    padding: 16px;
    font-size: 12px;
  }
}

:deep(.ant-table) {
  background: transparent !important;
}

:deep(.ant-table .ant-table-thead > tr > th) {
  background: transparent !important;
  color: #ffffff;
}

:deep(.ant-table .ant-table-tbody > tr > td) {
  background: transparent;
  color: #ffffff;
}

:deep(.ant-table .ant-table-tbody > tr:hover > td) {
  background: linear-gradient(
    rgba(24, 255, 249, 0.2),
    rgba(24, 255, 249, 0.01)
  ) !important;
}

:deep(.ant-empty .ant-empty-description) {
  color: #ffffff;
}

/* 隐藏滚动条，但仍可滚动 */
:deep(.ant-table-tbody) {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

:deep(.ant-table-tbody::-webkit-scrollbar) {
  display: none;
}

#echarts {
  height: 200px;
  width: 100%;
}

.video-box {
  height: 600px;
  color: #ffffff;
}
</style>
