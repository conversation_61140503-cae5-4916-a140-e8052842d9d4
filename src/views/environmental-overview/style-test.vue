<template>
  <div class="style-test-page">
    <h1>样式测试页面</h1>
    
    <!-- 测试基本布局 -->
    <div class="test-layout">
      <div class="left-panel">
        <h3>左侧面板</h3>
        <a-tree :tree-data="testTreeData" />
      </div>
      
      <div class="right-panel">
        <h3>右侧面板</h3>
        <a-card title="测试卡片">
          <p>这是一个测试卡片内容</p>
        </a-card>
      </div>
    </div>

    <!-- 测试表格样式 -->
    <div class="test-table">
      <h3>表格样式测试</h3>
      <a-table :data-source="testTableData" :pagination="false">
        <a-table-column title="名称" data-index="name" />
        <a-table-column title="值" data-index="value" />
        <a-table-column title="状态" data-index="status" />
      </a-table>
    </div>

    <!-- 测试按钮和表单 -->
    <div class="test-form">
      <h3>表单样式测试</h3>
      <a-form layout="inline">
        <a-form-item label="输入框">
          <a-input placeholder="请输入" />
        </a-form-item>
        <a-form-item label="选择器">
          <a-select placeholder="请选择" style="width: 120px">
            <a-select-option value="1">选项1</a-select-option>
            <a-select-option value="2">选项2</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-button type="primary">提交</a-button>
        </a-form-item>
      </a-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const testTreeData = ref([
  {
    key: '1',
    title: '节点1',
    children: [
      { key: '1-1', title: '子节点1-1' },
      { key: '1-2', title: '子节点1-2' }
    ]
  },
  {
    key: '2',
    title: '节点2',
    children: [
      { key: '2-1', title: '子节点2-1' }
    ]
  }
]);

const testTableData = ref([
  { key: '1', name: '测试项目1', value: '100', status: '正常' },
  { key: '2', name: '测试项目2', value: '200', status: '异常' },
  { key: '3', name: '测试项目3', value: '300', status: '正常' }
]);
</script>

<style lang="less" scoped>
.style-test-page {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;

  h1 {
    color: #333;
    text-align: center;
    margin-bottom: 30px;
  }

  h3 {
    color: #666;
    margin-bottom: 16px;
  }
}

.test-layout {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;

  .left-panel,
  .right-panel {
    flex: 1;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.test-table,
.test-form {
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

// 测试深色主题样式
.dark-theme {
  background: #0a1a2e;
  color: #fff;

  :deep(.ant-card) {
    background: rgba(11, 32, 87, 0.8);
    border: 1px solid #2b8dc5;
    color: #fff;
  }

  :deep(.ant-table) {
    background: transparent;

    .ant-table-thead > tr > th {
      background: rgba(11, 32, 87, 0.8);
      border-color: #2b8dc5;
      color: #fff;
    }

    .ant-table-tbody > tr > td {
      background: rgba(11, 32, 87, 0.6);
      border-color: #2b8dc5;
      color: #fff;
    }
  }

  :deep(.ant-input) {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid #2b8dc5;
    color: #fff;

    &::placeholder {
      color: rgba(255, 255, 255, 0.6);
    }
  }

  :deep(.ant-select-selector) {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid #2b8dc5;
    color: #fff;
  }
}
</style>
