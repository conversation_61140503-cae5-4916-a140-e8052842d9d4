<template>
  <div class="environmental-overview-page">
    <a-spin :spinning="fullscreenLoading" size="large">
      <!-- 背景 -->
      <div id="screen-map" class="screen-map"></div>
      <div class="main-background"></div>

      <!-- 左侧导航树 -->
      <div :class="['main-tree', isCollapsed ? 'main-tree-s' : '']">
        <div class="search">
          <a-input
            v-model:value="filterText"
            placeholder="搜索节点"
            allow-clear
          >
            <template #prefix>
              <SearchOutlined />
            </template>
          </a-input>
        </div>
        
        <div class="tree-wrapper">
          <a-tree
            v-if="mainModel === '0'"
            :tree-data="treeData"
            :filter-tree-node="filterNode"
            :field-names="defaultProps"
            @select="handleNodeClick"
          />
          <a-tree
            v-else
            :tree-data="treeDataJy"
            :filter-tree-node="filterNode"
            :field-names="defaultProps"
            @select="handleNodeClick"
          />
        </div>
        
        <!-- 收缩按钮 -->
        <div 
          :class="['left-img', isCollapsed ? 'left-img-s' : '']"
          @click="isCollapsed = !isCollapsed"
        >
          <img src="@/assets/images/left.png" alt="收缩" />
        </div>
      </div>

      <!-- 主体内容区域 -->
      <div :class="['main-body', isCollapsed ? 'main-body-s' : '']">
        <!-- 右上角控制区 -->
        <div class="control">
          <div class="control-div">
            <img
              class="img"
              src="@/assets/images/control1.png"
              alt="刷新"
              @click="refreshMap"
            />
            <img
              class="img"
              src="@/assets/images/control2.png"
              alt="放大"
              @click="() => mapScale(1)"
            />
            <img
              class="img"
              src="@/assets/images/control3.png"
              alt="缩小"
              @click="() => mapScale(-1)"
            />
          </div>
        </div>

        <!-- 模式选择 -->
        <div class="select">
          <a-select
            v-model:value="mainModel"
            placeholder="请选择模式"
            @change="mainChange"
          >
            <a-select-option value="0">污染源在线管理</a-select-option>
            <a-select-option value="1">甲烷管控模块</a-select-option>
          </a-select>
        </div>

        <!-- 内容卡片区域 -->
        <div class="card">
          <a-card v-if="mainModel === '0'" title="污染源在线管理" class="box-card">
            <p>这里是污染源在线管理的内容</p>
            <a-table :data-source="testTableData" :pagination="false">
              <a-table-column title="监测点" data-index="name" />
              <a-table-column title="数值" data-index="value" />
              <a-table-column title="状态" data-index="status" />
            </a-table>
          </a-card>
          
          <a-card v-else title="甲烷管控模块" class="box-card">
            <p>这里是甲烷管控模块的内容</p>
            <div style="height: 200px; background: rgba(255,255,255,0.1); display: flex; align-items: center; justify-content: center; color: #fff;">
              图表区域
            </div>
          </a-card>
        </div>

        <!-- 拖拽菜单 -->
        <div 
          class="draggable-menu"
          :style="{ left: x + 'px', top: y + 'px' }"
          @mousedown="startDrag"
        >
          <MenuOutlined class="menu-icon" />
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { SearchOutlined, MenuOutlined } from '@ant-design/icons-vue';

// 响应式数据
const fullscreenLoading = ref(false);
const isCollapsed = ref(false);
const mainModel = ref('0');
const filterText = ref('');
const x = ref(100);
const y = ref(100);

// 树形数据
const treeData = ref([
  {
    id: '1',
    label: '污染源监测点1',
    children: [
      { id: '1-1', label: '废水监测点1-1' },
      { id: '1-2', label: '废气监测点1-2' }
    ]
  },
  {
    id: '2',
    label: '污染源监测点2',
    children: [
      { id: '2-1', label: '废水监测点2-1' }
    ]
  }
]);

const treeDataJy = ref([
  {
    id: 'jy1',
    label: '甲烷监测点1',
    children: [
      { id: 'jy1-1', label: '甲烷点位1-1' }
    ]
  }
]);

const testTableData = ref([
  { key: '1', name: '监测点1', value: '100', status: '正常' },
  { key: '2', name: '监测点2', value: '200', status: '异常' },
  { key: '3', name: '监测点3', value: '300', status: '正常' }
]);

const defaultProps = {
  children: 'children',
  title: 'label',
  key: 'id'
};

// 方法
const filterNode = (searchValue: string, treeNode: any) => {
  return treeNode.label.toLowerCase().includes(searchValue.toLowerCase());
};

const handleNodeClick = (selectedKeys: string[], info: any) => {
  console.log('选中节点:', selectedKeys, info);
};

const refreshMap = () => {
  console.log('刷新地图');
};

const mapScale = (scale: number) => {
  console.log('地图缩放:', scale);
};

const mainChange = (value: string) => {
  mainModel.value = value;
  console.log('切换模式:', value);
};

const startDrag = (e: MouseEvent) => {
  const startX = e.clientX - x.value;
  const startY = e.clientY - y.value;

  const handleMouseMove = (e: MouseEvent) => {
    x.value = e.clientX - startX;
    y.value = e.clientY - startY;
  };

  const handleMouseUp = () => {
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  };

  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('mouseup', handleMouseUp);
};
</script>

<style lang="less" scoped>
.environmental-overview-page {
  position: relative;
  width: 100%;
  height: 100vh;
  background: #0a1a2e;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.screen-map {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1;
}

.main-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #0a1a2e 0%, #16213e 100%);
  z-index: 2;
}

.main-body {
  position: absolute;
  top: 0;
  left: 300px;
  right: 0;
  height: 100vh;
  transition: all 0.3s ease;

  &.main-body-s {
    left: 60px;
  }
}

.main-tree {
  position: absolute;
  top: 0;
  left: 0;
  width: 300px;
  height: 100vh;
  background: rgba(11, 32, 87, 0.8);
  border-right: 1px solid #2b8dc5;
  padding: 16px;
  z-index: 10;
  transition: all 0.3s ease;

  &.main-tree-s {
    width: 60px;
    
    .search,
    .tree-wrapper {
      display: none;
    }
  }

  .search {
    margin-bottom: 16px;
    
    :deep(.ant-input) {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid #2b8dc5;
      color: #fff;
      
      &::placeholder {
        color: rgba(255, 255, 255, 0.6);
      }
    }
  }

  .tree-wrapper {
    height: calc(100% - 60px);
    overflow-y: auto;
    
    :deep(.ant-tree) {
      background: transparent;
      color: #fff;
      
      .ant-tree-node-content-wrapper {
        color: #fff;
        
        &:hover {
          background: rgba(43, 141, 197, 0.3);
        }
        
        &.ant-tree-node-selected {
          background: rgba(43, 141, 197, 0.5);
        }
      }
      
      .ant-tree-switcher {
        color: #fff;
      }
    }
  }
}

.left-img {
  position: absolute;
  top: 50%;
  right: -20px;
  transform: translateY(-50%);
  width: 20px;
  height: 60px;
  cursor: pointer;
  z-index: 11;
  transition: all 0.3s ease;
}

.control {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;

  .control-div {
    display: flex;
    flex-direction: column;
    gap: 10px;

    .img {
      width: 40px;
      height: 40px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        transform: scale(1.1);
      }
    }
  }
}

.select {
  position: absolute;
  top: 20px;
  right: 100px;
  width: 200px;
  z-index: 10;

  :deep(.ant-select) {
    width: 100%;
    
    .ant-select-selector {
      background: rgba(11, 32, 87, 0.8);
      border: 1px solid #2b8dc5;
      color: #fff;
    }
    
    .ant-select-arrow {
      color: #fff;
    }
  }
}

.card {
  position: absolute;
  bottom: 20px;
  left: 20px;
  right: 20px;
  z-index: 5;
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.box-card {
  :deep(.ant-card) {
    background: rgba(11, 32, 87, 0.8);
    border: 1px solid #2b8dc5;
    color: #fff;

    .ant-card-head {
      border-color: #2b8dc5;
      background: rgba(11, 32, 87, 0.9);

      .ant-card-head-title {
        color: #fff;
      }
    }

    .ant-card-body {
      background: rgba(11, 32, 87, 0.6);
    }
  }

  :deep(.ant-table) {
    background: transparent;

    .ant-table-thead > tr > th {
      background: rgba(11, 32, 87, 0.8);
      border-color: #2b8dc5;
      color: #fff;
    }

    .ant-table-tbody > tr > td {
      background: rgba(11, 32, 87, 0.6);
      border-color: #2b8dc5;
      color: #fff;
    }

    .ant-table-tbody > tr:hover > td {
      background: rgba(43, 141, 197, 0.3);
    }
  }
}

.draggable-menu {
  position: absolute;
  width: 50px;
  height: 50px;
  background: rgba(43, 141, 197, 0.8);
  border-radius: 50%;
  cursor: move;
  z-index: 12;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(43, 141, 197, 1);
    transform: scale(1.1);
  }

  .menu-icon {
    color: #fff;
    font-size: 20px;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .main-tree {
    width: 250px;
  }
  
  .main-body {
    left: 250px;
    
    &.main-body-s {
      left: 60px;
    }
  }
}

@media (max-width: 768px) {
  .main-tree {
    width: 200px;
  }
  
  .main-body {
    left: 200px;
    
    &.main-body-s {
      left: 50px;
    }
  }
  
  .control {
    .control-div {
      .img {
        width: 30px;
        height: 30px;
      }
    }
  }
  
  .select {
    width: 150px;
  }
}
</style>
