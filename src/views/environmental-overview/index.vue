<template>
  <a-spin :spinning="fullscreenLoading" tip="拼命加载中...">
    <div class="environmental-overview-page">
      <div id="screen-map" class="screen-map"></div>
      <div class="main-background"></div>

      <!-- 左侧树形导航 -->
      <div :class="['main-tree', isCollapsed ? 'main-tree-s' : '']">
        <a-input
          v-model:value="filterText"
          class="search"
          placeholder="输入关键词"
          allow-clear
          size="small"
        >
          <template #prefix>
            <SearchOutlined />
          </template>
        </a-input>
        <div class="tree-wrapper">
          <a-tree
            v-if="mainModel === '0'"
            ref="monitorListRef"
            :tree-data="treeData"
            :field-names="defaultProps"
            :filter-tree-node="filterNode"
            :expand-on-click-node="false"
            @select="handleNodeClick"
          />
          <a-tree
            v-if="mainModel === '1'"
            ref="monitorListRef"
            :tree-data="treeDataJy"
            :field-names="defaultProps"
            :filter-tree-node="filterNode"
            :expand-on-click-node="false"
            @select="handleNodeClick"
          >
            <template #title="{ data }">
              <div class="custom-tree-node">
                <span>{{ data.label }}</span>
              </div>
            </template>
          </a-tree>
        </div>
      </div>

      <!-- 收缩按钮 -->
      <img
        src="@/assets/images/shouqi.png"
        alt=""
        :class="['left-img', isCollapsed ? 'left-img-s' : '']"
        @click="isCollapsed = !isCollapsed"
      />
      <img
        src="@/assets/images/shouqiicon.png"
        alt=""
        :class="['left-img-icon', isCollapsed ? 'left-img-icon-s' : '']"
        @click="isCollapsed = !isCollapsed"
      />

      <!-- 菜单图标 -->
      <div
        ref="menuRef"
        class="draggable-menu"
        :style="{ left: `${x}px`, top: `${y}px` }"
        :class="{ active: isDragging }"
        @mousedown="startDrag"
        @click="iconHandle"
      >
        <div class="menu-icon">
          <MenuOutlined />
        </div>
      </div>

      <div
        v-if="isMenu"
        :class="['main-tree']"
        style="background-color: rgba(11, 32, 87, 0.6)"
      >
        <menuDialog />
      </div>

      <!-- 地图控制按钮 -->
      <div :class="['control', isCollapsed ? 'control-s' : '']">
        <div class="control-div">
          <img
            class="img"
            src="@/assets/images/control1.png"
            alt=""
            @click="refreshMap"
          />
          <img class="img img1" src="@/assets/images/control3.png" alt="" />
          <div class="imgClick">
            <div @click="mapScale(0.5)"></div>
            <div @click="mapScale(-0.5)"></div>
          </div>
        </div>
      </div>

      <!-- 模式选择 -->
      <div :class="['select', isCollapsed && !isMenu ? 'select-s' : '']">
        <a-select
          v-model:value="mainModel"
          placeholder="请选择"
          @change="mainChange"
        >
          <a-select-option
            v-for="item in options"
            :key="item.value"
            :value="item.value"
          >
            {{ item.label }}
          </a-select-option>
        </a-select>
      </div>

      <!-- 污染源在线管理模块 -->
      <div v-if="mainModel === '0'" class="card">
        <div class="card-top">
          <card-collapse
            :title="'数据统计'"
            :height="'400px'"
            @child="childHandle"
          >
            <template #content>
              <div class="select-jy select-jy-jy" style="width: 800px">
                <a-form layout="inline" :model="environmentalData">
                  <a-form-item label="日期">
                    <a-date-picker
                      v-model:value="environmentalData.day"
                      placeholder="选择日期"
                    />
                  </a-form-item>
                  <a-form-item>
                    <a-button type="primary" @click="queryHandle"
                      >查询</a-button
                    >
                  </a-form-item>
                </a-form>
              </div>
              <div class="content-jy">
                <column-chart v-if="chartShow" :chart-data="columnDate" />
              </div>
            </template>
          </card-collapse>
        </div>
      </div>

      <!-- 实时监测数据表格 -->
      <div
        v-if="!isCollapsed1 && mainModel === '0'"
        class="card"
        style="margin-top: 80px"
      >
        <!-- 废水实时监测 -->
        <a-card class="box-card">
          <template #title>
            <span>废水实时监测</span>
            <a-select
              v-show="show1"
              v-model:value="siteId"
              placeholder="请选择"
              style="margin-bottom: 8px"
              @change="chooseHandle"
            >
              <a-select-option
                v-for="item in sites"
                :key="item.id"
                :value="item.id"
              >
                {{ item.siteName }}
              </a-select-option>
            </a-select>
            <a-button
              type="link"
              style="
                position: absolute;
                z-index: 1999;
                display: flex;
                justify-content: flex-end;
                margin-left: 1rem;
                pointer-events: fill;
              "
              @click="openHandle(0)"
            >
              <span v-if="!show1">展开</span>
              <span v-if="show1">收缩</span>
            </a-button>
          </template>
          <div v-if="!show1" style="width: 120px"></div>
          <a-table
            v-show="show1"
            ref="table1Ref"
            :data-source="tableData1"
            :pagination="false"
            :scroll="{ y: height }"
            :row-class-name="tableRowClassName"
            @mouseenter="
              () => rollingTable('table1', rollingModel.rollingTableStop)
            "
            @mouseleave="
              () => rollingTable('table1', rollingModel.rollingTableStart)
            "
          >
            <a-table-column
              title="单位"
              data-index="site"
              align="center"
              :ellipsis="true"
            />
            <a-table-column
              title="监测点"
              data-index="monitorName"
              align="center"
              :ellipsis="true"
            />
            <a-table-column
              title="监测时间"
              data-index="mTime"
              align="center"
              :ellipsis="true"
            />
            <a-table-column
              title="COD"
              data-index="cwater011"
              align="center"
              :ellipsis="true"
            />
            <a-table-column
              title="氨氮"
              data-index="cwater060"
              align="center"
              :ellipsis="true"
            />
          </a-table>
        </a-card>

        <!-- 废气实时监测 -->
        <a-card class="box-card">
          <template #title>
            <span>废气实时监测</span>
            <a-button
              type="link"
              style="
                position: absolute;
                z-index: 1999;
                display: flex;
                justify-content: flex-end;
                margin-left: 1rem;
                pointer-events: fill;
              "
              @click="openHandle(1)"
            >
              <span v-if="!show2">展开</span>
              <span v-if="show2">收缩</span>
            </a-button>
          </template>
          <a-table
            v-show="show2"
            ref="table2Ref"
            :data-source="tableData2"
            :pagination="false"
            :scroll="{ y: height }"
            :row-class-name="tableRowClassName"
            @mouseenter="
              () => rollingTable('table2', rollingModel.rollingTableStop)
            "
            @mouseleave="
              () => rollingTable('table2', rollingModel.rollingTableStart)
            "
          >
            <a-table-column
              title="单位"
              data-index="site"
              align="center"
              :ellipsis="true"
            />
            <a-table-column
              title="监测点"
              data-index="monitorName"
              align="center"
              :ellipsis="true"
            />
            <a-table-column
              title="监测时间"
              data-index="mTime"
              align="center"
              :ellipsis="true"
            />
            <a-table-column
              title="SO₂实测"
              data-index="cair02R"
              align="center"
              :ellipsis="true"
            />
            <a-table-column
              title="NOX实测"
              data-index="cair03R"
              align="center"
              :ellipsis="true"
            />
          </a-table>
        </a-card>

        <!-- 超标情况监测 -->
        <a-card class="box-card">
          <template #title>
            <span>超标情况监测</span>
            <a-button
              type="link"
              style="
                position: absolute;
                z-index: 1999;
                display: flex;
                justify-content: flex-end;
                margin-left: 1rem;
                pointer-events: fill;
              "
              @click="openHandle(2)"
            >
              <span v-if="!show3">展开</span>
              <span v-if="show3">收缩</span>
            </a-button>
          </template>
          <a-tabs
            v-show="show3"
            v-model:active-key="activeName"
            style="
              position: absolute;
              z-index: 1999;
              display: flex;
              justify-content: flex-end;
              margin-bottom: 16px;
              pointer-events: fill;
            "
            @change="handleClick"
          >
            <a-tab-pane key="0" tab="废水超标情况" />
            <a-tab-pane key="1" tab="废气超标情况" />
          </a-tabs>

          <!-- 废水超标表格 -->
          <a-table
            v-show="show3"
            v-if="activeName === '0'"
            ref="table3Ref"
            :data-source="tableData3"
            :pagination="false"
            :scroll="{ y: height }"
            :row-class-name="tableRowClassName"
            style="margin-top: 40px"
            @mouseenter="
              () => rollingTable('table3', rollingModel.rollingTableStop)
            "
            @mouseleave="
              () => rollingTable('table3', rollingModel.rollingTableStart)
            "
          >
            <a-table-column
              title="单位"
              data-index="site"
              align="center"
              :ellipsis="true"
            />
            <a-table-column
              title="监测点"
              data-index="monitorName"
              align="center"
              :ellipsis="true"
            />
            <a-table-column
              title="监测时间"
              data-index="mTime"
              align="center"
              :ellipsis="true"
            />
            <a-table-column
              title="COD"
              data-index="cwater011"
              align="center"
              :ellipsis="true"
            />
            <a-table-column
              title="氨氮"
              data-index="cwater060"
              align="center"
              :ellipsis="true"
            />
          </a-table>

          <!-- 废气超标表格 -->
          <a-table
            v-show="show3"
            v-if="activeName === '1'"
            ref="table4Ref"
            :data-source="tableData4"
            :pagination="false"
            :scroll="{ y: height }"
            :row-class-name="tableRowClassName"
            style="margin-top: 40px"
            @mouseenter="
              () => rollingTable('table4', rollingModel.rollingTableStop)
            "
            @mouseleave="
              () => rollingTable('table4', rollingModel.rollingTableStart)
            "
          >
            <a-table-column
              title="单位"
              data-index="site"
              align="center"
              :ellipsis="true"
            />
            <a-table-column
              title="监测点"
              data-index="monitorName"
              align="center"
              :ellipsis="true"
            />
            <a-table-column
              title="监测时间"
              data-index="mTime"
              align="center"
              :ellipsis="true"
            />
            <a-table-column
              title="SO₂实测"
              data-index="cair02R"
              align="center"
              :ellipsis="true"
            />
            <a-table-column
              title="NOX实测"
              data-index="cair03R"
              align="center"
              :ellipsis="true"
            />
          </a-table>
        </a-card>
      </div>

      <!-- 甲烷管控模块 -->
      <div v-if="mainModel === '1'" class="card card-jy">
        <div class="card-top">
          <card-collapse title="甲烷管控模块" @child="childHandle">
            <template v-if="chartShow" #content>
              <div class="select-jy select-jy-jy">
                <a-form layout="inline" :model="alarmSituationData">
                  <a-form-item label="点位列表">
                    <a-select v-model:value="typeModeJy" placeholder="请选择">
                      <a-select-option
                        v-for="item in methaneOptions"
                        :key="item.id"
                        :value="item.id"
                      >
                        {{ item.deviceMark }}
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                  <a-form-item label="开始时间">
                    <a-range-picker
                      v-model:value="timeArr"
                      show-time
                      format="YYYY-MM-DD HH:mm:ss"
                      :placeholder="['开始日期', '结束日期']"
                    />
                  </a-form-item>
                  <a-form-item>
                    <a-button type="primary" @click="onSubmit('0')">
                      实时查询
                    </a-button>
                    <a-button type="primary" @click="onSubmit('1')">
                      历史查询
                    </a-button>
                  </a-form-item>
                </a-form>
              </div>
              <a-row>
                <a-col :span="12">
                  <line-chart
                    :chart-data="data1.value"
                    :time-data="data1.time"
                    :height="200"
                    :legend="`浓度检测值 实时值${value1}ppm.m`"
                    :title="'浓度检测值'"
                  />
                </a-col>
                <a-col :span="12">
                  <line-chart
                    :chart-data="data2.value"
                    :time-data="data2.time"
                    :height="200"
                    :legend="`浓度检测报警 实时值${value2} ${value3}`"
                    :title="'浓度检测报警'"
                  />
                </a-col>
              </a-row>
            </template>
          </card-collapse>
        </div>

        <!-- 气体泄漏报警统计 -->
        <div class="card-top">
          <card-collapse title="气体泄漏报警统计">
            <template #content="{ isCollapsed }">
              <div v-if="isCollapsed">
                <div class="select-jy select-jy-jy">
                  <a-form layout="inline" :model="alarmSituationData">
                    <a-form-item label="点位列表">
                      <a-select
                        v-model:value="typeModeJyQt"
                        placeholder="请选择"
                      >
                        <a-select-option
                          v-for="item in methaneOptions"
                          :key="item.id"
                          :value="item.id"
                        >
                          {{ item.deviceMark }}
                        </a-select-option>
                      </a-select>
                    </a-form-item>
                    <a-form-item label="开始时间">
                      <a-range-picker
                        v-model:value="timeArrQt"
                        show-time
                        format="YYYY-MM-DD HH:mm:ss"
                        :placeholder="['开始日期', '结束日期']"
                      />
                    </a-form-item>
                    <a-form-item>
                      <a-button type="primary" @click="onSubmitQt"
                        >查询</a-button
                      >
                      <a-button type="primary" @click="resetHandle"
                        >重置</a-button
                      >
                    </a-form-item>
                  </a-form>
                </div>
                <div style="width: 922px">
                  <a-table
                    :data-source="alarmTableData"
                    :pagination="false"
                    :scroll="{ y: 300 }"
                    :row-class-name="tableRowClassName"
                  >
                    <a-table-column
                      title="点位名称"
                      data-index="pointName"
                      align="center"
                      :ellipsis="true"
                    />
                    <a-table-column
                      title="报警时间"
                      data-index="alarmsDateStr"
                      align="center"
                      :ellipsis="true"
                    />
                    <a-table-column
                      title="备注"
                      data-index="remark"
                      align="center"
                      :ellipsis="true"
                    />
                  </a-table>
                  <a-pagination
                    v-model:current="alarmCurrentPage"
                    v-model:page-size="alarmPageSize"
                    :total="alarmTotal"
                    :page-size-options="['20', '40', '60', '80', '100']"
                    show-size-changer
                    show-quick-jumper
                    show-total
                    style="margin-top: 20px; text-align: right"
                    @change="handleCurrentChange"
                    @show-size-change="handleSizeChange"
                  />
                </div>
              </div>
            </template>
          </card-collapse>
        </div>
      </div>

      <!-- 详情弹窗 -->
      <details-dialog
        ref="detailsDialogRef"
        :device-list="deviceList"
        :site-short-name="siteShortName"
      />

      <!-- 预览图片 -->
      <transition name="fade">
        <img
          v-if="dialogVisible"
          :src="dialogImageUrl"
          alt="Preview"
          class="preview-image"
        />
      </transition>
    </div>
  </a-spin>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onBeforeUnmount, nextTick, computed } from 'vue';
import { useRouter } from 'vue-router';
import { SearchOutlined, MenuOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';

// 导入组件
import CardCollapse from '@/components/card-collapse.vue';
import MenuDialog from '@/components/menu.vue';
import ColumnChart from './components/ColumnChart.vue';
import LineChart from './components/LineChart.vue';
import DetailsDialog from './components/details-dialog.vue';

// 导入API
import {
  getTreeNode,
  initDeviceMap,
  getPointInfo,
  getMonitorStatistics,
  getMonitorList,
  initEnvinfoWarnList,
  getJoinDept,
  getDeptPoint,
  getRealTimeData,
  getHistoryData,
  getAlarmPage
} from '@/http/environmental-overview';

// 类型定义
interface TreeNode {
  id: string;
  label: string;
  children?: TreeNode[];
  [key: string]: any;
}

interface OptionItem {
  label: string;
  value: string;
}

interface EnvironmentalData {
  day: Dayjs | null;
}

interface AlarmSituationData {
  startTime: string;
  endTime: string;
  pointId: string;
}

interface TableDataItem {
  id: string;
  site: string;
  monitorName: string;
  mTime: string;
  cwater011?: number;
  cwater060?: number;
  cair02R?: number;
  cair03R?: number;
  [key: string]: any;
}

interface SiteItem {
  id: string;
  siteName: string;
}

interface MethaneOption {
  id: string;
  deviceMark: string;
}

interface ChartDataItem {
  value: number[];
  time: string[];
}

interface RollingModel {
  rollingTableStart: string;
  rollingTableStop: string;
}

interface AlarmTableDataItem {
  pointName: string;
  alarmsDateStr: string;
  remark: string;
  [key: string]: any;
}

// 响应式数据
const router = useRouter();

// 基础状态
const fullscreenLoading = ref(false);
const isCollapsed = ref(false);
const isCollapsed1 = ref(false);
const isMenu = ref(false);
const chartShow = ref(true);
const filterText = ref('');
const mainModel = ref('0');
const activeName = ref('0');
const dialogVisible = ref(false);
const dialogImageUrl = ref('');

// 树形数据
const treeData = ref<TreeNode[]>([]);
const treeDataJy = ref<TreeNode[]>([]);

// 表格数据
const tableData1 = ref<TableDataItem[]>([]);
const tableData2 = ref<TableDataItem[]>([]);
const tableData3 = ref<TableDataItem[]>([]);
const tableData4 = ref<TableDataItem[]>([]);

// 表格显示控制
const show1 = ref(true);
const show2 = ref(true);
const show3 = ref(true);

// 站点相关
const sites = ref<SiteItem[]>([]);
const siteId = ref('');
const siteShortName = ref('');
const deviceList = ref<any[]>([]);

// 甲烷相关
const methaneOptions = ref<MethaneOption[]>([]);
const typeModeJy = ref('');
const typeModeJyQt = ref('');
const timeArr = ref<[Dayjs | null, Dayjs | null]>([null, null]);
const timeArrQt = ref<[Dayjs | null, Dayjs | null]>([null, null]);

// 图表数据
const columnDate = ref<number[][]>([]);
const data1 = ref<ChartDataItem>({ value: [], time: [] });
const data2 = ref<ChartDataItem>({ value: [], time: [] });
const value1 = ref(0);
const value2 = ref(0);
const value3 = ref('');

// 报警表格
const alarmTableData = ref<AlarmTableDataItem[]>([]);
const alarmCurrentPage = ref(1);
const alarmPageSize = ref(20);
const alarmTotal = ref(0);

// 拖拽相关
const x = ref(50);
const y = ref(50);
const isDragging = ref(false);

// 地图相关
let map: any = null;

// 表单数据
const environmentalData = reactive<EnvironmentalData>({
  day: null
});

const alarmSituationData = reactive<AlarmSituationData>({
  startTime: '',
  endTime: '',
  pointId: ''
});

// 表格滚动相关
const rollingModel = reactive<RollingModel>({
  rollingTableStart: 'start',
  rollingTableStop: 'stop'
});

// refs
const monitorListRef = ref();
const menuRef = ref<HTMLElement>();
const detailsDialogRef = ref();
const table1Ref = ref();
const table2Ref = ref();
const table3Ref = ref();
const table4Ref = ref();

// 计算属性
const height = computed(() => 280);

const defaultProps = {
  children: 'children',
  title: 'label',
  key: 'id'
};

const options: OptionItem[] = [
  { label: '污染源在线管理', value: '0' },
  { label: '甲烷管控模块', value: '1' }
];

// 方法定义
const filterNode = (searchValue: string, treeNode: TreeNode) => {
  return treeNode.label.toLowerCase().includes(searchValue.toLowerCase());
};

const handleNodeClick = (selectedKeys: string[], info: any) => {
  console.log('选中节点:', selectedKeys, info);
  if (selectedKeys.length > 0) {
    const nodeData = info.node;
    if (nodeData.children && nodeData.children.length > 0) {
      // 是父节点，展开/收起
      return;
    }
    // 是叶子节点，处理点击事件
    handleLeafNodeClick(nodeData);
  }
};

const handleLeafNodeClick = (nodeData: TreeNode) => {
  console.log('点击叶子节点:', nodeData);
  if (mainModel.value === '0') {
    // 污染源在线管理模式
    detailsDialogRef.value?.openDialog({
      id: nodeData.id,
      sitePathName: nodeData.sitePathName || nodeData.label
    });
  } else if (mainModel.value === '1') {
    // 甲烷管控模式
    typeModeJy.value = nodeData.id;
    onSubmit('0'); // 默认查询实时数据
  }
};

const childHandle = (data: { queryParam: boolean }) => {
  isCollapsed1.value = !data.queryParam;
};

const queryHandle = () => {
  if (!environmentalData.day) {
    message.warning('请选择日期');
    return;
  }
  
  fullscreenLoading.value = true;
  const date = environmentalData.day.format('YYYY-MM-DD');
  
  // 获取数据统计
  getMonitorStatistics({ date })
    .then((res: any) => {
      if (res.code === 200) {
        columnDate.value = res.data;
      } else {
        message.error(res.msg || '查询失败');
      }
    })
    .catch(() => {
      message.error('查询失败');
    })
    .finally(() => {
      fullscreenLoading.value = false;
    });
};

const openHandle = (type: number) => {
  switch (type) {
    case 0:
      show1.value = !show1.value;
      break;
    case 1:
      show2.value = !show2.value;
      break;
    case 2:
      show3.value = !show3.value;
      break;
  }
};

const chooseHandle = (value: string) => {
  siteId.value = value;
  // 根据选择的站点更新数据
  loadMonitorData();
};

const handleClick = (key: string) => {
  activeName.value = key;
  loadExceedingData();
};

const tableRowClassName = ({ index }: { index: number }) => {
  return index % 2 === 1 ? 'table-striped' : '';
};

const rollingTable = (tableId: string, action: string) => {
  // 表格滚动控制逻辑
  console.log(`表格 ${tableId} 执行 ${action} 操作`);
};

const mainChange = (value: string) => {
  mainModel.value = value;
  chartShow.value = true;
  
  if (value === '0') {
    // 污染源在线管理模式
    loadTreeData();
    loadMonitorData();
  } else if (value === '1') {
    // 甲烷管控模式
    loadMethaneTreeData();
    loadMethaneOptions();
  }
};

const onSubmit = (type: string) => {
  if (!typeModeJy.value) {
    message.warning('请选择点位');
    return;
  }
  
  const params = {
    pointId: typeModeJy.value,
    startTime: timeArr.value[0]?.format('YYYY-MM-DD HH:mm:ss') || '',
    endTime: timeArr.value[1]?.format('YYYY-MM-DD HH:mm:ss') || ''
  };
  
  fullscreenLoading.value = true;
  
  const apiCall = type === '0' ? getRealTimeData : getHistoryData;
  
  apiCall(params)
    .then((res: any) => {
      if (res.code === 200) {
        processMethaneData(res.data);
      } else {
        message.error(res.msg || '查询失败');
      }
    })
    .catch(() => {
      message.error('查询失败');
    })
    .finally(() => {
      fullscreenLoading.value = false;
    });
};

const onSubmitQt = () => {
  if (!typeModeJyQt.value) {
    message.warning('请选择点位');
    return;
  }
  
  const params = {
    pointId: typeModeJyQt.value,
    startTime: timeArrQt.value[0]?.format('YYYY-MM-DD HH:mm:ss') || '',
    endTime: timeArrQt.value[1]?.format('YYYY-MM-DD HH:mm:ss') || '',
    current: alarmCurrentPage.value,
    size: alarmPageSize.value
  };
  
  getAlarmPage(params)
    .then((res: any) => {
      if (res.code === 200) {
        alarmTableData.value = res.data.records || [];
        alarmTotal.value = res.data.total || 0;
      } else {
        message.error(res.msg || '查询失败');
      }
    })
    .catch(() => {
      message.error('查询失败');
    });
};

const resetHandle = () => {
  typeModeJyQt.value = '';
  timeArrQt.value = [null, null];
  alarmTableData.value = [];
  alarmCurrentPage.value = 1;
  alarmTotal.value = 0;
};

const handleCurrentChange = (page: number) => {
  alarmCurrentPage.value = page;
  onSubmitQt();
};

const handleSizeChange = (current: number, size: number) => {
  alarmPageSize.value = size;
  alarmCurrentPage.value = 1;
  onSubmitQt();
};

// 拖拽相关方法
const startDrag = (e: MouseEvent) => {
  isDragging.value = true;
  const startX = e.clientX - x.value;
  const startY = e.clientY - y.value;
  
  const handleMouseMove = (e: MouseEvent) => {
    x.value = e.clientX - startX;
    y.value = e.clientY - startY;
  };
  
  const handleMouseUp = () => {
    isDragging.value = false;
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  };
  
  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('mouseup', handleMouseUp);
};

const iconHandle = () => {
  if (!isDragging.value) {
    isMenu.value = !isMenu.value;
  }
};

// 地图相关方法
const refreshMap = () => {
  if (map) {
    map.setView([30.6598, 104.0633], 10);
  }
};

const mapScale = (delta: number) => {
  if (map) {
    const currentZoom = map.getZoom();
    map.setZoom(currentZoom + delta);
  }
};

// 数据加载方法
const loadTreeData = () => {
  getTreeNode({ type: 'pollution' })
    .then((res: any) => {
      if (res.code === 200) {
        treeData.value = res.data || [];
      }
    })
    .catch(() => {
      message.error('加载树形数据失败');
    });
};

const loadMethaneTreeData = () => {
  getJoinDept()
    .then((res: any) => {
      if (res.code === 200) {
        treeDataJy.value = res.data || [];
      }
    })
    .catch(() => {
      message.error('加载甲烷树形数据失败');
    });
};

const loadMethaneOptions = () => {
  getDeptPoint()
    .then((res: any) => {
      if (res.code === 200) {
        methaneOptions.value = res.data || [];
      }
    })
    .catch(() => {
      message.error('加载甲烷点位失败');
    });
};

const loadMonitorData = () => {
  // 加载废水实时监测数据
  getMonitorList({ monitorType: '10' })
    .then((res: any) => {
      if (res.code === 200) {
        tableData1.value = res.data || [];
      }
    })
    .catch(() => {
      message.error('加载废水监测数据失败');
    });
    
  // 加载废气实时监测数据
  getMonitorList({ monitorType: '20' })
    .then((res: any) => {
      if (res.code === 200) {
        tableData2.value = res.data || [];
      }
    })
    .catch(() => {
      message.error('加载废气监测数据失败');
    });
    
  // 加载超标数据
  loadExceedingData();
};

const loadExceedingData = () => {
  const monitorType = activeName.value === '0' ? '10' : '20';
  
  initEnvinfoWarnList({ monitorType })
    .then((res: any) => {
      if (res.code === 200) {
        if (activeName.value === '0') {
          tableData3.value = res.data || [];
        } else {
          tableData4.value = res.data || [];
        }
      }
    })
    .catch(() => {
      message.error('加载超标数据失败');
    });
};

const processMethaneData = (data: any[]) => {
  if (!Array.isArray(data) || data.length === 0) {
    data1.value = { value: [], time: [] };
    data2.value = { value: [], time: [] };
    value1.value = 0;
    value2.value = 0;
    value3.value = '';
    return;
  }
  
  const values1: number[] = [];
  const values2: number[] = [];
  const times: string[] = [];
  
  data.forEach((item: any) => {
    values1.push(item.concentration || 0);
    values2.push(item.alarmValue || 0);
    times.push(item.time || '');
  });
  
  data1.value = { value: values1, time: times };
  data2.value = { value: values2, time: times };
  
  // 获取最新值
  const latest = data[data.length - 1];
  if (latest) {
    value1.value = latest.concentration || 0;
    value2.value = latest.alarmValue || 0;
    value3.value = latest.alarmStatus || '';
  }
};

// 初始化方法
const initMap = () => {
  // 初始化地图
  nextTick(() => {
    // 这里应该初始化您的地图组件
    console.log('初始化地图');
  });
};

const initData = () => {
  // 设置默认日期为今天
  environmentalData.day = dayjs();
  
  // 加载初始数据
  if (mainModel.value === '0') {
    loadTreeData();
    loadMonitorData();
  } else {
    loadMethaneTreeData();
    loadMethaneOptions();
  }
  
  // 查询今日数据统计
  queryHandle();
};

// 生命周期
onMounted(() => {
  initMap();
  initData();
});

onBeforeUnmount(() => {
  // 清理资源
  if (map) {
    map.remove();
  }
});
</script>

<style lang="less" scoped>
.environmental-overview-page {
  position: relative;
  width: 100%;
  height: 100vh;
  background: #0a1a2e;
  overflow: hidden;
}

.screen-map {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}

.main-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('@/assets/images/main-background.png') no-repeat center center;
  background-size: cover;
  z-index: 2;
}

.main-tree {
  position: absolute;
  top: 20px;
  left: 20px;
  width: 320px;
  height: calc(100vh - 40px);
  background: rgba(11, 32, 87, 0.8);
  border: 1px solid #2b8dc5;
  border-radius: 8px;
  z-index: 100;
  transition: all 0.3s ease;
  
  &.main-tree-s {
    width: 60px;
    
    .search,
    .tree-wrapper {
      display: none;
    }
  }
  
  .search {
    margin: 16px;
    width: calc(100% - 32px);
    
    :deep(.ant-input) {
      background: rgba(0, 0, 0, 0.3);
      border-color: #2b8dc5;
      color: #fff;
      
      &::placeholder {
        color: #999;
      }
    }
  }
  
  .tree-wrapper {
    height: calc(100% - 80px);
    overflow: auto;
    padding: 0 16px;
    
    :deep(.ant-tree) {
      background: transparent;
      color: #fff;
      
      .ant-tree-node-content-wrapper {
        color: #fff;
        
        &:hover {
          background: rgba(43, 141, 197, 0.3);
        }
        
        &.ant-tree-node-selected {
          background: rgba(43, 141, 197, 0.5);
        }
      }
      
      .ant-tree-switcher {
        color: #fff;
      }
    }
  }
}

.left-img,
.left-img-icon {
  position: absolute;
  top: 50%;
  left: 340px;
  width: 20px;
  height: 40px;
  cursor: pointer;
  z-index: 101;
  transition: all 0.3s ease;
  transform: translateY(-50%);
  
  &.left-img-s,
  &.left-img-icon-s {
    left: 80px;
  }
}

.left-img-icon {
  top: calc(50% + 45px);
}

.draggable-menu {
  position: absolute;
  width: 50px;
  height: 50px;
  background: rgba(43, 141, 197, 0.8);
  border-radius: 50%;
  cursor: move;
  z-index: 102;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  
  &.active {
    background: rgba(43, 141, 197, 1);
  }
  
  .menu-icon {
    color: #fff;
    font-size: 20px;
  }
}

.control {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 100;
  transition: all 0.3s ease;
  
  &.control-s {
    right: 20px;
  }
  
  .control-div {
    display: flex;
    flex-direction: column;
    gap: 10px;
    
    .img {
      width: 40px;
      height: 40px;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        transform: scale(1.1);
      }
      
      &.img1 {
        position: relative;
      }
    }
    
    .imgClick {
      position: absolute;
      top: 50px;
      right: 0;
      width: 40px;
      height: 40px;
      
      div {
        width: 100%;
        height: 50%;
        cursor: pointer;
      }
    }
  }
}

.select {
  position: absolute;
  top: 20px;
  right: 80px;
  width: 200px;
  z-index: 100;
  transition: all 0.3s ease;
  
  &.select-s {
    right: 80px;
  }
  
  :deep(.ant-select) {
    width: 100%;
    
    .ant-select-selector {
      background: rgba(0, 0, 0, 0.3);
      border-color: #2b8dc5;
      color: #fff;
    }
    
    .ant-select-arrow {
      color: #fff;
    }
  }
}

.card {
  position: absolute;
  bottom: 20px;
  left: 360px;
  right: 20px;
  max-height: calc(100vh - 200px);
  z-index: 100;
  transition: all 0.3s ease;
  
  &.card-jy {
    left: 360px;
    right: 20px;
    bottom: 20px;
    height: calc(100vh - 200px);
    overflow-y: auto;
  }
  
  .card-top {
    margin-bottom: 20px;
  }
}

.box-card {
  margin-bottom: 20px;
  
  :deep(.ant-card) {
    background: rgba(11, 32, 87, 0.8);
    border: 1px solid #2b8dc5;
    
    .ant-card-head {
      border-color: #2b8dc5;
      background: rgba(43, 141, 197, 0.3);
      
      .ant-card-head-title {
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
    }
    
    .ant-card-body {
      color: #fff;
    }
  }
  
  :deep(.ant-table) {
    background: transparent;
    
    .ant-table-thead > tr > th {
      background: rgba(43, 141, 197, 0.3);
      border-color: #2b8dc5;
      color: #fff;
    }
    
    .ant-table-tbody > tr > td {
      background: transparent;
      border-color: #2b8dc5;
      color: #fff;
    }
    
    .ant-table-tbody > tr:hover > td {
      background: rgba(43, 141, 197, 0.2);
    }
    
    .table-striped > td {
      background: rgba(0, 0, 0, 0.1);
    }
  }
  
  :deep(.ant-tabs) {
    .ant-tabs-tab {
      color: #fff;
      
      &.ant-tabs-tab-active {
        color: #2b8dc5;
      }
    }
    
    .ant-tabs-ink-bar {
      background: #2b8dc5;
    }
  }
}

.select-jy {
  margin-bottom: 20px;
  
  &.select-jy-jy {
    background: rgba(0, 0, 0, 0.2);
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 20px;
  }
  
  :deep(.ant-form-item-label > label) {
    color: #fff;
  }
  
  :deep(.ant-input) {
    background: rgba(0, 0, 0, 0.3);
    border-color: #2b8dc5;
    color: #fff;
    
    &::placeholder {
      color: #999;
    }
  }
  
  :deep(.ant-select-selector) {
    background: rgba(0, 0, 0, 0.3) !important;
    border-color: #2b8dc5 !important;
    color: #fff !important;
  }
  
  :deep(.ant-picker) {
    background: rgba(0, 0, 0, 0.3);
    border-color: #2b8dc5;
    color: #fff;
    
    input {
      color: #fff;
      background: transparent;
    }
  }
}

.content-jy {
  min-height: 300px;
}

.preview-image {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-width: 90vw;
  max-height: 90vh;
  z-index: 9999;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  border-radius: 8px;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 隐藏滚动条
:deep(.ant-table-body) {
  scrollbar-width: none;
  -ms-overflow-style: none;
  
  &::-webkit-scrollbar {
    display: none;
  }
}

// 分页器样式
:deep(.ant-pagination) {
  .ant-pagination-item,
  .ant-pagination-prev,
  .ant-pagination-next,
  .ant-pagination-jump-prev,
  .ant-pagination-jump-next {
    background: rgba(0, 0, 0, 0.3);
    border-color: #2b8dc5;
    
    a {
      color: #fff;
    }
    
    &:hover {
      background: rgba(43, 141, 197, 0.3);
      border-color: #2b8dc5;
      
      a {
        color: #fff;
      }
    }
  }
  
  .ant-pagination-item-active {
    background: #2b8dc5;
    border-color: #2b8dc5;
    
    a {
      color: #fff;
    }
  }
  
  .ant-pagination-options {
    .ant-select-selector {
      background: rgba(0, 0, 0, 0.3);
      border-color: #2b8dc5;
      color: #fff;
    }
  }
  
  .ant-pagination-total-text,
  .ant-pagination-simple-pager {
    color: #fff;
  }
}
</style>
