<template>
  <a-spin :spinning="fullscreenLoading" tip="拼命加载中...">
    <div class="environmental-overview-page">
      <div id="screen-map" class="screen-map"></div>
      <div class="main-background"></div>

      <!-- 左侧树形导航 -->
      <div :class="['main-tree', isCollapsed ? 'main-tree-s' : '']">
        <a-input
          v-model:value="filterText"
          class="search"
          placeholder="输入关键词"
          allow-clear
          size="small"
        >
          <template #prefix>
            <SearchOutlined />
          </template>
        </a-input>
        <div class="tree-wrapper">
          <a-tree
            v-if="mainModel === '0'"
            ref="monitorListRef"
            :tree-data="treeData"
            :field-names="defaultProps"
            :filter-tree-node="filterNode"
            :expand-on-click-node="false"
            @select="handleNodeClick"
          />
          <a-tree
            v-if="mainModel === '1'"
            ref="monitorListRef"
            :tree-data="treeDataJy"
            :field-names="defaultProps"
            :filter-tree-node="filterNode"
            :expand-on-click-node="false"
            @select="handleNodeClick"
          >
            <template #title="{ data }">
              <div class="custom-tree-node">
                <span>{{ data.label }}</span>
              </div>
            </template>
          </a-tree>
        </div>
      </div>

      <!-- 收缩按钮 -->
      <img
        src="@/assets/images/shouqi.png"
        alt=""
        :class="['left-img', isCollapsed ? 'left-img-s' : '']"
        @click="isCollapsed = !isCollapsed"
      />
      <img
        src="@/assets/images/shouqiicon.png"
        alt=""
        :class="['left-img-icon', isCollapsed ? 'left-img-icon-s' : '']"
        @click="isCollapsed = !isCollapsed"
      />

      <!-- 菜单图标 -->
      <div
        ref="menuRef"
        class="draggable-menu"
        :style="{ left: `${x}px`, top: `${y}px` }"
        :class="{ active: isDragging }"
        @mousedown="startDrag"
        @click="iconHandle"
      >
        <div class="menu-icon">
          <MenuOutlined />
        </div>
      </div>

      <div
        v-if="isMenu"
        :class="['main-tree']"
        style="background-color: rgba(11, 32, 87, 0.6)"
      >
        <menuDialog />
      </div>

      <!-- 地图控制按钮 -->
      <div :class="['control', isCollapsed ? 'control-s' : '']">
        <div class="control-div">
          <img
            class="img"
            src="@/assets/images/control1.png"
            alt=""
            @click="refreshMap"
          />
          <img class="img img1" src="@/assets/images/control3.png" alt="" />
          <div class="imgClick">
            <div @click="mapScale(0.5)"></div>
            <div @click="mapScale(-0.5)"></div>
          </div>
        </div>
      </div>

      <!-- 模式选择 -->
      <div :class="['select', isCollapsed && !isMenu ? 'select-s' : '']">
        <a-select
          v-model:value="mainModel"
          placeholder="请选择"
          @change="mainChange"
        >
          <a-select-option
            v-for="item in options"
            :key="item.value"
            :value="item.value"
          >
            {{ item.label }}
          </a-select-option>
        </a-select>
      </div>

      <!-- 污染源在线管理模块 -->
      <div v-if="mainModel === '0'" class="card">
        <div class="card-top">
          <card-collapse
            :title="'数据统计'"
            :height="'400px'"
            @child="childHandle"
          >
            <template #content>
              <div class="select-jy select-jy-jy" style="width: 800px">
                <a-form layout="inline" :model="environmentalData">
                  <a-form-item label="日期">
                    <a-date-picker
                      v-model:value="environmentalData.day"
                      placeholder="选择日期"
                    />
                  </a-form-item>
                  <a-form-item>
                    <a-button type="primary" @click="queryHandle"
                      >查询</a-button
                    >
                  </a-form-item>
                </a-form>
              </div>
              <div class="content-jy">
                <column-chart v-if="chartShow" :chart-data="columnDate" />
              </div>
            </template>
          </card-collapse>
        </div>
      </div>

      <!-- 实时监测数据表格 -->
      <div
        v-if="!isCollapsed1 && mainModel === '0'"
        class="card"
        style="margin-top: 80px"
      >
        <!-- 废水实时监测 -->
        <a-card class="box-card">
          <template #title>
            <span>废水实时监测</span>
            <a-select
              v-show="show1"
              v-model:value="siteId"
              placeholder="请选择"
              style="margin-bottom: 8px"
              @change="chooseHandle"
            >
              <a-select-option
                v-for="item in sites"
                :key="item.id"
                :value="item.id"
              >
                {{ item.siteName }}
              </a-select-option>
            </a-select>
            <a-button
              type="link"
              style="
                position: absolute;
                z-index: 1999;
                display: flex;
                justify-content: flex-end;
                margin-left: 1rem;
                pointer-events: fill;
              "
              @click="openHandle(0)"
            >
              <span v-if="!show1">展开</span>
              <span v-if="show1">收缩</span>
            </a-button>
          </template>
          <div v-if="!show1" style="width: 120px"></div>
          <a-table
            v-show="show1"
            ref="table1Ref"
            :data-source="tableData1"
            :pagination="false"
            :scroll="{ y: height }"
            :row-class-name="tableRowClassName"
            @mouseenter="
              () => rollingTable('table1', rollingModel.rollingTableStop)
            "
            @mouseleave="
              () => rollingTable('table1', rollingModel.rollingTableStart)
            "
          >
            <a-table-column
              title="单位"
              data-index="site"
              align="center"
              :ellipsis="true"
            />
            <a-table-column
              title="监测点"
              data-index="monitorName"
              align="center"
              :ellipsis="true"
            />
            <a-table-column
              title="监测时间"
              data-index="mTime"
              align="center"
              :ellipsis="true"
            />
            <a-table-column
              title="COD"
              data-index="cwater011"
              align="center"
              :ellipsis="true"
            />
            <a-table-column
              title="氨氮"
              data-index="cwater060"
              align="center"
              :ellipsis="true"
            />
          </a-table>
        </a-card>

        <!-- 废气实时监测 -->
        <a-card class="box-card">
          <template #title>
            <span>废气实时监测</span>
            <a-button
              type="link"
              style="
                position: absolute;
                z-index: 1999;
                display: flex;
                justify-content: flex-end;
                margin-left: 1rem;
                pointer-events: fill;
              "
              @click="openHandle(1)"
            >
              <span v-if="!show2">展开</span>
              <span v-if="show2">收缩</span>
            </a-button>
          </template>
          <a-table
            v-show="show2"
            ref="table2Ref"
            :data-source="tableData2"
            :pagination="false"
            :scroll="{ y: height }"
            :row-class-name="tableRowClassName"
            @mouseenter="
              () => rollingTable('table2', rollingModel.rollingTableStop)
            "
            @mouseleave="
              () => rollingTable('table2', rollingModel.rollingTableStart)
            "
          >
            <a-table-column
              title="单位"
              data-index="site"
              align="center"
              :ellipsis="true"
            />
            <a-table-column
              title="监测点"
              data-index="monitorName"
              align="center"
              :ellipsis="true"
            />
            <a-table-column
              title="监测时间"
              data-index="mTime"
              align="center"
              :ellipsis="true"
            />
            <a-table-column
              title="SO₂实测"
              data-index="cair02R"
              align="center"
              :ellipsis="true"
            />
            <a-table-column
              title="NOX实测"
              data-index="cair03R"
              align="center"
              :ellipsis="true"
            />
          </a-table>
        </a-card>

        <!-- 超标情况监测 -->
        <a-card class="box-card">
          <template #title>
            <span>超标情况监测</span>
            <a-button
              type="link"
              style="
                position: absolute;
                z-index: 1999;
                display: flex;
                justify-content: flex-end;
                margin-left: 1rem;
                pointer-events: fill;
              "
              @click="openHandle(2)"
            >
              <span v-if="!show3">展开</span>
              <span v-if="show3">收缩</span>
            </a-button>
          </template>
          <a-tabs
            v-show="show3"
            v-model:active-key="activeName"
            style="
              position: absolute;
              z-index: 1999;
              display: flex;
              justify-content: flex-end;
              margin-bottom: 16px;
              pointer-events: fill;
            "
            @change="handleClick"
          >
            <a-tab-pane key="0" tab="废水超标情况" />
            <a-tab-pane key="1" tab="废气超标情况" />
          </a-tabs>

          <!-- 废水超标表格 -->
          <a-table
            v-show="show3"
            v-if="activeName === '0'"
            ref="table3Ref"
            :data-source="tableData3"
            :pagination="false"
            :scroll="{ y: height }"
            :row-class-name="tableRowClassName"
            style="margin-top: 40px"
            @mouseenter="
              () => rollingTable('table3', rollingModel.rollingTableStop)
            "
            @mouseleave="
              () => rollingTable('table3', rollingModel.rollingTableStart)
            "
          >
            <a-table-column
              title="单位"
              data-index="site"
              align="center"
              :ellipsis="true"
            />
            <a-table-column
              title="监测点"
              data-index="monitorName"
              align="center"
              :ellipsis="true"
            />
            <a-table-column
              title="监测时间"
              data-index="mTime"
              align="center"
              :ellipsis="true"
            />
            <a-table-column
              title="COD"
              data-index="cwater011"
              align="center"
              :ellipsis="true"
            />
            <a-table-column
              title="氨氮"
              data-index="cwater060"
              align="center"
              :ellipsis="true"
            />
          </a-table>

          <!-- 废气超标表格 -->
          <a-table
            v-show="show3"
            v-if="activeName === '1'"
            ref="table4Ref"
            :data-source="tableData4"
            :pagination="false"
            :scroll="{ y: height }"
            :row-class-name="tableRowClassName"
            style="margin-top: 40px"
            @mouseenter="
              () => rollingTable('table4', rollingModel.rollingTableStop)
            "
            @mouseleave="
              () => rollingTable('table4', rollingModel.rollingTableStart)
            "
          >
            <a-table-column
              title="单位"
              data-index="site"
              align="center"
              :ellipsis="true"
            />
            <a-table-column
              title="监测点"
              data-index="monitorName"
              align="center"
              :ellipsis="true"
            />
            <a-table-column
              title="监测时间"
              data-index="mTime"
              align="center"
              :ellipsis="true"
            />
            <a-table-column
              title="SO₂实测"
              data-index="cair02R"
              align="center"
              :ellipsis="true"
            />
            <a-table-column
              title="NOX实测"
              data-index="cair03R"
              align="center"
              :ellipsis="true"
            />
          </a-table>
        </a-card>
      </div>

      <!-- 甲烷管控模块 -->
      <div v-if="mainModel === '1'" class="card card-jy">
        <div class="card-top">
          <card-collapse title="甲烷管控模块" @child="childHandle">
            <template v-if="chartShow" #content>
              <div class="select-jy select-jy-jy">
                <a-form layout="inline" :model="alarmSituationData">
                  <a-form-item label="点位列表">
                    <a-select v-model:value="typeModeJy" placeholder="请选择">
                      <a-select-option
                        v-for="item in methaneOptions"
                        :key="item.id"
                        :value="item.id"
                      >
                        {{ item.deviceMark }}
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                  <a-form-item label="开始时间">
                    <a-range-picker
                      v-model:value="timeArr"
                      show-time
                      format="YYYY-MM-DD HH:mm:ss"
                      :placeholder="['开始日期', '结束日期']"
                    />
                  </a-form-item>
                  <a-form-item>
                    <a-button type="primary" @click="onSubmit('0')">
                      实时查询
                    </a-button>
                    <a-button type="primary" @click="onSubmit('1')">
                      历史查询
                    </a-button>
                  </a-form-item>
                </a-form>
              </div>
              <a-row>
                <a-col :span="12">
                  <line-chart
                    :chart-data="data1.value"
                    :time-data="data1.time"
                    :height="200"
                    :legend="`浓度检测值 实时值${value1}ppm.m`"
                    :title="'浓度检测值'"
                  />
                </a-col>
                <a-col :span="12">
                  <line-chart
                    :chart-data="data2.value"
                    :time-data="data2.time"
                    :height="200"
                    :legend="`浓度检测报警 实时值${value2} ${value3}`"
                    :title="'浓度检测报警'"
                  />
                </a-col>
              </a-row>
            </template>
          </card-collapse>
        </div>

        <!-- 气体泄漏报警统计 -->
        <div class="card-top">
          <card-collapse title="气体泄漏报警统计">
            <template #content="{ isCollapsed }">
              <div v-if="isCollapsed">
                <div class="select-jy select-jy-jy">
                  <a-form layout="inline" :model="alarmSituationData">
                    <a-form-item label="点位列表">
                      <a-select
                        v-model:value="typeModeJyQt"
                        placeholder="请选择"
                      >
                        <a-select-option
                          v-for="item in methaneOptions"
                          :key="item.id"
                          :value="item.id"
                        >
                          {{ item.deviceMark }}
                        </a-select-option>
                      </a-select>
                    </a-form-item>
                    <a-form-item label="开始时间">
                      <a-range-picker
                        v-model:value="timeArrQt"
                        show-time
                        format="YYYY-MM-DD HH:mm:ss"
                        :placeholder="['开始日期', '结束日期']"
                      />
                    </a-form-item>
                    <a-form-item>
                      <a-button type="primary" @click="onSubmitQt"
                        >查询</a-button
                      >
                      <a-button type="primary" @click="resetHandle"
                        >重置</a-button
                      >
                    </a-form-item>
                  </a-form>
                </div>
                <div style="width: 922px">
                  <a-table
                    :data-source="alarmTableData"
                    :pagination="false"
                    :scroll="{ y: 300 }"
                    :row-class-name="tableRowClassName"
                  >
                    <a-table-column
                      title="点位名称"
                      data-index="pointName"
                      align="center"
                      :ellipsis="true"
                    />
                    <a-table-column
                      title="报警时间"
                      data-index="alarmsDateStr"
                      align="center"
                      :ellipsis="true"
                    />
                    <a-table-column
                      title="备注"
                      data-index="remark"
                      align="center"
                      :ellipsis="true"
                    />
                  </a-table>
                  <a-pagination
                    v-model:current="alarmCurrentPage"
                    v-model:page-size="alarmPageSize"
                    :total="alarmTotal"
                    :page-size-options="['20', '40', '60', '80', '100']"
                    show-size-changer
                    show-quick-jumper
                    :show-total="(total: number, range: [number, number]) => `共 ${total} 条记录，第 ${range[0]}-${range[1]} 条`"
                    style="margin-top: 20px; text-align: right"
                    @change="handleCurrentChange"
                    @show-size-change="handleSizeChange"
                  />
                </div>
              </div>
            </template>
          </card-collapse>
        </div>
      </div>

      <!-- 详情弹窗 -->
      <details-dialog
        ref="detailsDialogRef"
        :device-list="deviceList"
        :site-short-name="siteShortName"
      />

      <!-- 预览图片 -->
      <transition name="fade">
        <img
          v-if="dialogVisible"
          :src="dialogImageUrl"
          alt="Preview"
          class="preview-image"
        />
      </transition>
    </div>
  </a-spin>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onBeforeUnmount, nextTick, computed } from 'vue';
import { useRouter } from 'vue-router';
import { SearchOutlined, MenuOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';

// 导入组件
import CardCollapse from '@/components/card-collapse.vue';
import MenuDialog from '@/components/menu.vue';
import ColumnChart from './components/ColumnChart.vue';
import LineChart from './components/LineChart.vue';
import DetailsDialog from './components/details-dialog.vue';

// 导入API
import {
  getTreeNode,
  initDeviceMap,
  getPointInfo,
  getMonitorStatistics,
  getMonitorList,
  initEnvinfoWarnList,
  getJoinDept,
  getDeptPoint,
  getRealTimeData,
  getHistoryData,
  getAlarmPage
} from '@/http/environmental-overview';

// 类型定义
interface TreeNode {
  id: string;
  label: string;
  children?: TreeNode[];
  [key: string]: any;
}

interface OptionItem {
  label: string;
  value: string;
}

interface EnvironmentalData {
  day: Dayjs | undefined;
}

interface AlarmSituationData {
  startTime: string;
  endTime: string;
  pointId: string;
}

interface TableDataItem {
  id: string;
  site: string;
  monitorName: string;
  mTime: string;
  cwater011?: number;
  cwater060?: number;
  cair02R?: number;
  cair03R?: number;
  [key: string]: any;
}

interface RollingModel {
  rollingTableStart: string;
  rollingTableStop: string;
}

interface ChartData {
  value: number[];
  time: string[];
}

// 响应式数据
const router = useRouter();

// 基础状态
const fullscreenLoading = ref(false);
const isCollapsed = ref(false);
const isCollapsed1 = ref(false);
const isMenu = ref(false);
const chartShow = ref(true);
const mainModel = ref('0');
const filterText = ref('');

// 树形数据
const treeData = ref<TreeNode[]>([]);
const treeDataJy = ref<TreeNode[]>([]);

// 表格数据
const tableData1 = ref<TableDataItem[]>([]);
const tableData2 = ref<TableDataItem[]>([]);
const tableData3 = ref<TableDataItem[]>([]);
const tableData4 = ref<TableDataItem[]>([]);

// 表单数据
const environmentalData = reactive<EnvironmentalData>({
  day: undefined
});

const alarmSituationData = reactive<AlarmSituationData>({
  startTime: '',
  endTime: '',
  pointId: ''
});

// 图表数据
const columnDate = ref<number[][]>([]);
const data1 = ref<ChartData>({ value: [], time: [] });
const data2 = ref<ChartData>({ value: [], time: [] });

// 甲烷模块相关
const methaneOptions = ref<any[]>([]);
const typeModeJy = ref('');
const typeModeJyQt = ref('');
const timeArr = ref<[Dayjs, Dayjs] | undefined>(undefined);
const timeArrQt = ref<[Dayjs, Dayjs] | undefined>(undefined);
const value1 = ref(0);
const value2 = ref(0);
const value3 = ref('');

// 报警数据
const alarmTableData = ref<any[]>([]);
const alarmCurrentPage = ref(1);
const alarmPageSize = ref(20);
const alarmTotal = ref(0);

// 表格显示控制
const show1 = ref(true);
const show2 = ref(true);
const show3 = ref(true);
const activeName = ref('0');

// 站点相关
const sites = ref<any[]>([]);
const siteId = ref('');
const deviceList = ref<any[]>([]);
const siteShortName = ref('');

// 拖拽相关
const x = ref(100);
const y = ref(100);
const isDragging = ref(false);

// 地图相关
let map: any = null;

// 滚动模型
const rollingModel: RollingModel = {
  rollingTableStart: 'start',
  rollingTableStop: 'stop'
};

// 图片预览
const dialogVisible = ref(false);
const dialogImageUrl = ref('');

// refs
const monitorListRef = ref();
const menuRef = ref<HTMLElement>();
const detailsDialogRef = ref();
const table1Ref = ref();
const table2Ref = ref();
const table3Ref = ref();
const table4Ref = ref();

// 计算属性
const height = computed(() => 280);

const defaultProps = {
  children: 'children',
  title: 'label',
  key: 'id'
};

const options: OptionItem[] = [
  { label: '污染源在线管理', value: '0' },
  { label: '甲烷管控模块', value: '1' }
];

// 方法定义
const filterNode = (searchValue: string, treeNode: TreeNode) => {
  return treeNode.label.toLowerCase().includes(searchValue.toLowerCase());
};

const handleNodeClick = (selectedKeys: string[], info: any) => {
  console.log('选中节点:', selectedKeys, info);
  if (selectedKeys.length > 0) {
    const nodeData = info.node;
    if (nodeData.children && nodeData.children.length > 0) {
      // 是父节点，展开/收起
      return;
    }
    // 是叶子节点，处理点击事件
    handleLeafNodeClick(nodeData);
  }
};

const handleLeafNodeClick = (nodeData: TreeNode) => {
  console.log('点击叶子节点:', nodeData);
  if (mainModel.value === '0') {
    // 污染源在线管理模式
    detailsDialogRef.value?.openDialog({
      id: nodeData.id,
      sitePathName: nodeData.sitePathName || nodeData.label
    });
  } else if (mainModel.value === '1') {
    // 甲烷管控模式
    typeModeJy.value = nodeData.id;
    onSubmit('0'); // 默认查询实时数据
  }
};

const childHandle = (data: { queryParam: boolean }) => {
  isCollapsed1.value = !data.queryParam;
};

const queryHandle = () => {
  if (!environmentalData.day) {
    message.error('请选择日期');
    return;
  }

  const dateStr = environmentalData.day.format('YYYY-MM-DD');

  getMonitorStatistics({
    startTime: dateStr + ' 00:00:00',
    endTime: dateStr + ' 23:59:59'
  }).then((res: any) => {
    if (res.code === 200) {
      // 处理统计数据
      columnDate.value = res.data || [];
    }
  }).catch(() => {
    message.error('查询失败');
  });
};

const openHandle = (index: number) => {
  switch (index) {
    case 0:
      show1.value = !show1.value;
      break;
    case 1:
      show2.value = !show2.value;
      break;
    case 2:
      show3.value = !show3.value;
      break;
  }
};

const chooseHandle = (value: string) => {
  siteId.value = value;
  // 根据选择的站点加载数据
  loadMonitorData();
};

const handleClick = (key: string) => {
  activeName.value = key;
  loadExceedingData();
};

const tableRowClassName = ({ index }: { index: number }) => {
  return index % 2 === 1 ? 'table-striped' : '';
};

const rollingTable = (tableId: string, action: string) => {
  // 表格滚动控制逻辑
  console.log(`表格 ${tableId} 执行 ${action} 操作`);
};

const mainChange = (value: string) => {
  mainModel.value = value;
  chartShow.value = true;

  if (value === '0') {
    // 污染源在线管理模式
    loadTreeData();
    loadMonitorData();
  } else if (value === '1') {
    // 甲烷管控模式
    loadMethaneTreeData();
    loadMethaneOptions();
  }
};

// 拖拽相关方法
const startDrag = (e: MouseEvent) => {
  isDragging.value = true;
  const startX = e.clientX - x.value;
  const startY = e.clientY - y.value;

  const handleMouseMove = (e: MouseEvent) => {
    x.value = e.clientX - startX;
    y.value = e.clientY - startY;
  };

  const handleMouseUp = () => {
    isDragging.value = false;
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  };

  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('mouseup', handleMouseUp);
};

const iconHandle = () => {
  isMenu.value = !isMenu.value;
};

// 地图相关方法
const refreshMap = () => {
  console.log('刷新地图');
  // 重新加载地图数据
  loadMapDevices();

  // 如果有地图实例，重置视图
  if (map) {
    // 重置地图视图到初始状态
    console.log('重置地图视图');
  }
};

const mapScale = (scale: number) => {
  console.log('地图缩放:', scale);

  if (map) {
    // 获取当前缩放级别
    const currentZoom = map.getZoom ? map.getZoom() : 10;
    const newZoom = currentZoom + scale;

    // 设置新的缩放级别
    if (map.setZoom) {
      map.setZoom(Math.max(1, Math.min(18, newZoom)));
    }
  }
};

// 甲烷模块方法
const onSubmit = (type: string) => {
  if (!typeModeJy.value) {
    message.error('请选择点位');
    return;
  }

  const params: any = {
    pointId: typeModeJy.value,
    type
  };

  if (timeArr.value) {
    params.startTime = timeArr.value[0].format('YYYY-MM-DD HH:mm:ss');
    params.endTime = timeArr.value[1].format('YYYY-MM-DD HH:mm:ss');
  }

  if (type === '0') {
    // 实时查询
    getRealTimeData(params).then((res: any) => {
      if (res.code === 200) {
        processRealTimeData(res.data);
      }
    }).catch(() => {
      message.error('查询实时数据失败');
    });
  } else {
    // 历史查询
    getHistoryData(params).then((res: any) => {
      if (res.code === 200) {
        processHistoryData(res.data);
      }
    }).catch(() => {
      message.error('查询历史数据失败');
    });
  }
};

const processRealTimeData = (data: any) => {
  // 处理实时数据
  if (data && data.length > 0) {
    const timeData: string[] = [];
    const valueData: number[] = [];

    data.forEach((item: any) => {
      timeData.push(item.time);
      valueData.push(item.value);
    });

    data1.value = { time: timeData, value: valueData };
    value1.value = data[data.length - 1]?.value || 0;
  }
};

const processHistoryData = (data: any) => {
  // 处理历史数据
  if (data && data.length > 0) {
    const timeData: string[] = [];
    const valueData: number[] = [];

    data.forEach((item: any) => {
      timeData.push(item.time);
      valueData.push(item.value);
    });

    data2.value = { time: timeData, value: valueData };
    value2.value = data[data.length - 1]?.value || 0;
    value3.value = data[data.length - 1]?.unit || '';
  }
};

const onSubmitQt = () => {
  if (!typeModeJyQt.value) {
    message.error('请选择点位');
    return;
  }

  const params: any = {
    current: alarmCurrentPage.value,
    size: alarmPageSize.value,
    pointId: typeModeJyQt.value
  };

  if (timeArrQt.value) {
    params.startTime = timeArrQt.value[0].format('YYYY-MM-DD HH:mm:ss');
    params.endTime = timeArrQt.value[1].format('YYYY-MM-DD HH:mm:ss');
  }

  getAlarmPage(params).then((res: any) => {
    if (res.code === 200) {
      alarmTableData.value = res.data.records || [];
      alarmTotal.value = res.data.total || 0;
    }
  }).catch(() => {
    message.error('查询报警数据失败');
  });
};

const resetHandle = () => {
  typeModeJyQt.value = '';
  timeArrQt.value = undefined;
  alarmTableData.value = [];
  alarmCurrentPage.value = 1;
  alarmTotal.value = 0;
};

const handleCurrentChange = (page: number) => {
  alarmCurrentPage.value = page;
  onSubmitQt();
};

const handleSizeChange = (size: number) => {
  alarmPageSize.value = size;
  alarmCurrentPage.value = 1;
  onSubmitQt();
};

// 数据加载方法
const loadTreeData = () => {
  getTreeNode().then((res: any) => {
    if (res.code === 200) {
      treeData.value = res.data || [];
    }
  }).catch(() => {
    message.error('加载树形数据失败');
  });
};

const loadMethaneTreeData = () => {
  getJoinDept().then((res: any) => {
    if (res.code === 200) {
      treeDataJy.value = res.data || [];
    }
  }).catch(() => {
    message.error('加载甲烷树形数据失败');
  });
};

const loadMethaneOptions = () => {
  getDeptPoint({ deptId: '' }).then((res: any) => {
    if (res.code === 200) {
      methaneOptions.value = res.data || [];
    }
  }).catch(() => {
    message.error('加载甲烷点位失败');
  });
};

const loadMonitorData = () => {
  // 加载废水实时监测数据
  getMonitorList({ monitorType: '10' })
    .then((res: any) => {
      if (res.code === 200) {
        tableData1.value = res.data || [];
      }
    })
    .catch(() => {
      message.error('加载废水监测数据失败');
    });

  // 加载废气实时监测数据
  getMonitorList({ monitorType: '20' })
    .then((res: any) => {
      if (res.code === 200) {
        tableData2.value = res.data || [];
      }
    })
    .catch(() => {
      message.error('加载废气监测数据失败');
    });

  // 加载超标数据
  loadExceedingData();
};

const loadExceedingData = () => {
  const monitorType = activeName.value === '0' ? '10' : '20';

  initEnvinfoWarnList({ monitorType })
    .then((res: any) => {
      if (res.code === 200) {
        if (activeName.value === '0') {
          tableData3.value = res.data || [];
        } else {
          tableData4.value = res.data || [];
        }
      }
    })
    .catch(() => {
      message.error('加载超标数据失败');
    });
};

// 初始化方法
const initMap = () => {
  // 初始化地图
  nextTick(() => {
    const mapContainer = document.getElementById('screen-map');
    if (mapContainer) {
      // 这里可以初始化地图组件，比如 Leaflet、高德地图等
      console.log('初始化地图容器');

      // 加载地图上的设备点位
      loadMapDevices();
    }
  });
};

const loadMapDevices = () => {
  initDeviceMap({ deptId: '', monitorType: '' })
    .then((res: any) => {
      if (res.code === 200) {
        const devices = res.data || [];
        console.log('加载地图设备点位:', devices);
        // 在地图上显示设备点位
        renderMapDevices(devices);
      }
    })
    .catch(() => {
      message.error('加载地图设备失败');
    });
};

const renderMapDevices = (devices: any[]) => {
  // 在地图上渲染设备点位
  devices.forEach(device => {
    console.log('渲染设备点位:', device);
    // 这里可以在地图上添加标记点
  });
};

const initData = () => {
  // 设置默认日期为今天
  environmentalData.day = dayjs();

  // 加载初始数据
  if (mainModel.value === '0') {
    loadTreeData();
    loadMonitorData();
    // 查询今日数据统计
    queryHandle();
  } else {
    loadMethaneTreeData();
    loadMethaneOptions();
  }
};

// 生命周期
onMounted(() => {
  initMap();
  initData();
});

onBeforeUnmount(() => {
  // 清理资源
  if (map) {
    map.remove();
  }
});
</script>

<style lang="less" scoped>
.environmental-overview-page {
  position: relative;
  width: 100%;
  height: 100vh;
  background: #0a1a2e;
  overflow: hidden;
}

.screen-map {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}

.main-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #0a1a2e 0%, #16213e 100%);
  z-index: 2;
}

.main-tree {
  position: absolute;
  top: 20px;
  left: 20px;
  width: 300px;
  height: calc(100vh - 40px);
  background: rgba(11, 32, 87, 0.8);
  border: 1px solid #2b8dc5;
  border-radius: 8px;
  padding: 16px;
  z-index: 10;
  transition: all 0.3s ease;

  &.main-tree-s {
    width: 60px;

    .search {
      display: none;
    }

    .tree-wrapper {
      display: none;
    }
  }

  .search {
    margin-bottom: 16px;

    :deep(.ant-input) {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid #2b8dc5;
      color: #fff;

      &::placeholder {
        color: rgba(255, 255, 255, 0.6);
      }
    }
  }

  .tree-wrapper {
    height: calc(100% - 60px);
    overflow-y: auto;

    :deep(.ant-tree) {
      background: transparent;
      color: #fff;

      .ant-tree-node-content-wrapper {
        color: #fff;

        &:hover {
          background: rgba(43, 141, 197, 0.3);
        }

        &.ant-tree-node-selected {
          background: rgba(43, 141, 197, 0.5);
        }
      }

      .ant-tree-switcher {
        color: #fff;
      }
    }
  }
}

.left-img, .left-img-icon {
  position: absolute;
  top: 50%;
  left: 340px;
  transform: translateY(-50%);
  width: 20px;
  height: 60px;
  cursor: pointer;
  z-index: 11;
  transition: all 0.3s ease;

  &.left-img-s, &.left-img-icon-s {
    left: 100px;
  }
}

.left-img-icon {
  left: 360px;

  &.left-img-icon-s {
    left: 120px;
  }
}

.draggable-menu {
  position: absolute;
  width: 50px;
  height: 50px;
  background: rgba(43, 141, 197, 0.8);
  border-radius: 50%;
  cursor: move;
  z-index: 12;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &.active {
    background: rgba(43, 141, 197, 1);
    transform: scale(1.1);
  }

  .menu-icon {
    color: #fff;
    font-size: 20px;
  }
}

.control {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;
  transition: all 0.3s ease;

  &.control-s {
    right: 20px;
  }

  .control-div {
    display: flex;
    flex-direction: column;
    gap: 10px;

    .img {
      width: 40px;
      height: 40px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        transform: scale(1.1);
      }

      &.img1 {
        position: relative;
      }
    }

    .imgClick {
      position: absolute;
      top: 50px;
      right: 0;
      display: flex;
      flex-direction: column;

      div {
        width: 40px;
        height: 20px;
        cursor: pointer;
        background: rgba(43, 141, 197, 0.8);
        border: 1px solid #2b8dc5;

        &:first-child {
          border-bottom: none;
        }

        &:hover {
          background: rgba(43, 141, 197, 1);
        }
      }
    }
  }
}

.select {
  position: absolute;
  top: 20px;
  right: 100px;
  width: 200px;
  z-index: 10;
  transition: all 0.3s ease;

  &.select-s {
    right: 100px;
  }

  :deep(.ant-select) {
    width: 100%;

    .ant-select-selector {
      background: rgba(11, 32, 87, 0.8);
      border: 1px solid #2b8dc5;
      color: #fff;
    }

    .ant-select-arrow {
      color: #fff;
    }
  }
}

.card {
  position: absolute;
  bottom: 20px;
  left: 20px;
  right: 20px;
  z-index: 10;

  &.card-jy {
    left: 340px;
  }

  .card-top {
    margin-bottom: 16px;
  }
}

.box-card {
  margin-bottom: 16px;

  :deep(.ant-card) {
    background: rgba(11, 32, 87, 0.8);
    border: 1px solid #2b8dc5;
    color: #fff;

    .ant-card-head {
      border-color: #2b8dc5;
      background: rgba(11, 32, 87, 0.9);

      .ant-card-head-title {
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
    }

    .ant-card-body {
      background: rgba(11, 32, 87, 0.6);
    }
  }
}

.select-jy {
  margin-bottom: 16px;

  &.select-jy-jy {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  :deep(.ant-form-item) {
    margin-bottom: 0;

    .ant-form-item-label {
      color: #fff;
    }

    .ant-input, .ant-select-selector, .ant-picker {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid #2b8dc5;
      color: #fff;

      &::placeholder {
        color: rgba(255, 255, 255, 0.6);
      }
    }

    .ant-btn {
      margin-left: 8px;
    }
  }
}

.content-jy {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.ant-table) {
  background: transparent;

  .ant-table-thead > tr > th {
    background: rgba(11, 32, 87, 0.8);
    border-color: #2b8dc5;
    color: #fff;
  }

  .ant-table-tbody > tr > td {
    background: rgba(11, 32, 87, 0.6);
    border-color: #2b8dc5;
    color: #fff;
  }

  .ant-table-tbody > tr:hover > td {
    background: rgba(43, 141, 197, 0.3);
  }

  .ant-table-tbody > tr.table-striped > td {
    background: rgba(11, 32, 87, 0.4);
  }

  .ant-empty {
    .ant-empty-description {
      color: #fff;
    }
  }
}

:deep(.ant-tabs) {
  .ant-tabs-nav {
    .ant-tabs-tab {
      color: rgba(255, 255, 255, 0.7);

      &.ant-tabs-tab-active {
        color: #fff;
      }
    }

    .ant-tabs-ink-bar {
      background: #2b8dc5;
    }
  }
}

:deep(.ant-pagination) {
  .ant-pagination-item {
    background: rgba(11, 32, 87, 0.8);
    border-color: #2b8dc5;

    a {
      color: #fff;
    }

    &.ant-pagination-item-active {
      background: #2b8dc5;
      border-color: #2b8dc5;
    }
  }

  .ant-pagination-prev, .ant-pagination-next {
    background: rgba(11, 32, 87, 0.8);
    border-color: #2b8dc5;

    .ant-pagination-item-link {
      color: #fff;
    }
  }

  .ant-pagination-options {
    .ant-select-selector {
      background: rgba(11, 32, 87, 0.8);
      border-color: #2b8dc5;
      color: #fff;
    }
  }

  .ant-pagination-total-text {
    color: #fff;
  }
}

.preview-image {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-width: 90vw;
  max-height: 90vh;
  z-index: 1000;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
}

.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

.custom-tree-node {
  display: flex;
  align-items: center;
  width: 100%;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(43, 141, 197, 0.6);
  border-radius: 3px;

  &:hover {
    background: rgba(43, 141, 197, 0.8);
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .main-tree {
    width: 250px;
  }

  .left-img, .left-img-icon {
    left: 270px;

    &.left-img-s, &.left-img-icon-s {
      left: 80px;
    }
  }

  .card {
    &.card-jy {
      left: 270px;
    }
  }

  .select-jy {
    &.select-jy-jy {
      width: 600px;
    }
  }
}

@media (max-width: 768px) {
  .environmental-overview-page {
    overflow-x: auto;
  }

  .main-tree {
    width: 200px;
    height: calc(50vh - 20px);

    &.main-tree-s {
      width: 50px;
    }
  }

  .left-img, .left-img-icon {
    left: 220px;

    &.left-img-s, &.left-img-icon-s {
      left: 70px;
    }
  }

  .control {
    top: 10px;
    right: 10px;

    .control-div {
      .img {
        width: 30px;
        height: 30px;
      }
    }
  }

  .select {
    top: 10px;
    right: 60px;
    width: 150px;
  }

  .card {
    bottom: 10px;
    left: 10px;
    right: 10px;

    &.card-jy {
      left: 220px;
      right: 10px;
    }
  }

  .box-card {
    :deep(.ant-card-head) {
      .ant-card-head-title {
        font-size: 14px;
      }
    }
  }

  .select-jy {
    &.select-jy-jy {
      width: 100%;
      flex-direction: column;
      gap: 8px;
    }
  }

  .draggable-menu {
    width: 40px;
    height: 40px;

    .menu-icon {
      font-size: 16px;
    }
  }
}

@media (max-width: 480px) {
  .main-tree {
    width: 150px;

    &.main-tree-s {
      width: 40px;
    }
  }

  .left-img, .left-img-icon {
    left: 170px;
    width: 15px;
    height: 50px;

    &.left-img-s, &.left-img-icon-s {
      left: 60px;
    }
  }

  .card {
    &.card-jy {
      left: 170px;
    }
  }

  .select {
    width: 120px;
  }

  .content-jy {
    height: 200px;
  }

  :deep(.ant-table) {
    font-size: 12px;

    .ant-table-thead > tr > th,
    .ant-table-tbody > tr > td {
      padding: 8px 4px;
    }
  }

  :deep(.ant-form-item) {
    .ant-form-item-label {
      font-size: 12px;
    }
  }
}
</style>
